# Integration Status Tracker

Last Updated: 2025-08-27T19:56:01.381Z

| Integration | Status | Last Checked | Last Updated | Notes |
| ----------- | ------ | ------------ | ------------ | ----- |
| ----------- | ------ | 2025-08-21T02:42:40.119Z | N/A | ----- |
| UniFi Protect | error | 2025-08-27T19:54:31.372Z | N/A | Error: Failed to connect to all UniFi Protect instances: Instance A: instance.api.bootstrap is not a function |
| Google Forms | active | 2025-08-27T19:54:30.220Z | 2025-08-27T19:54:30.220Z | Integration is properly authenticated using service account. |
| SkyportCloud | error | 2025-08-27T19:54:30.592Z | N/A | Error: Request failed with status code 404 |
| UniFi Access | not_configured | 2025-08-27T19:54:30.510Z | N/A | Authentication failed. Check credentials and try again. |
| UniFi Network | error | 2025-08-27T19:54:35.812Z | N/A | Error: Failed to connect to UniFi Network Controller at **********:8443. Error: Authentication failed: Error: API Error: 404 - "" |
| Lenel S2 NetBox | error | 2025-08-27T19:56:01.381Z | N/A | Authentication error: getaddrinfo ENOTFOUND host-172-16-0-71 |
| RADIUS | active | 2025-08-27T19:54:33.159Z | 2025-08-27T19:54:33.159Z | RADIUS server is running with static authentication. |
| WiiM | error | 2025-08-27T19:55:05.929Z | N/A | Error: connect ENETUNREACH ************:443 |
| Google Admin | active | 2025-08-27T19:54:58.081Z | 2025-08-27T19:54:58.081Z | Integration is properly authenticated using service account. |
