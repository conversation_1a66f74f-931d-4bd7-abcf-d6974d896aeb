import React, { useState, useEffect, useRef, lazy, Suspense } from 'react';
import { 
  Box, 
  Grid, 
  Paper, 
  Typography, 
  Button, 
  IconButton, 
  Divider, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon, 
  ListItemSecondaryAction,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
  Alert,
  Tooltip,
  Slider,
  Chip,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import { 
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  LocationOn as LocationOnIcon,
  Thermostat as ThermostatIcon,
  MeetingRoom as DoorIcon,
  Videocam as CameraIcon,
  WaterDrop as WaterIcon,
  ElectricBolt as PowerIcon,
  Router as NetworkIcon,
  Lightbulb as LightIcon,
  AcUnit as HvacIcon,
  LocalFireDepartment as FireIcon,
  Sensors as MotionIcon,
  MoreHoriz as CustomIcon,
  ZoomIn as ZoomInIcon,
  ZoomOut as ZoomOutIcon,
  PanTool as PanIcon,
  Save as SaveIcon,
  ViewInAr as View3DIcon,
  Architecture as View2DIcon
} from '@mui/icons-material';
import buildingManagementService from '../../../services/buildingManagementService';

const FloorPlan3DEditor = lazy(() => import('../../../components/FloorPlan/FloorPlan3DEditor'));

// Map icon types to their respective Material-UI icons
const iconTypeMap = {
  temperature: ThermostatIcon,
  door: DoorIcon,
  camera: CameraIcon,
  motion: MotionIcon,
  light: LightIcon,
  hvac: HvacIcon,
  fire: FireIcon,
  water: WaterIcon,
  power: PowerIcon,
  network: NetworkIcon,
  custom: CustomIcon
};

const FloorplanEditor = ({ floor, onClose }) => {
  const [icons, setIcons] = useState([]);
  const [selectedIcon, setSelectedIcon] = useState(null);
  const [iconDialogOpen, setIconDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [zoom, setZoom] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  const [draggedIcon, setDraggedIcon] = useState(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });
  const [viewPosition, setViewPosition] = useState({ x: 0, y: 0 });
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const [editorMode, setEditorMode] = useState('icons');
  const [show3DEditor, setShow3DEditor] = useState(false);
  const [iconFormData, setIconFormData] = useState({
    name: '',
    type: 'temperature',
    customType: '',
    color: '#1976d2',
    status: 'normal',
    data: {
      value: '',
      unit: '',
      min: '',
      max: ''
    },
    deviceId: '',
    integrationSource: '',
    metadata: {
      description: '',
      room: '',
      notes: ''
    }
  });
  
  const containerRef = useRef(null);
  const floorplanRef = useRef(null);
  
  // Fetch icons for the floor
  useEffect(() => {
    const fetchIcons = async () => {
      try {
        setLoading(true);
        const iconsData = await buildingManagementService.getFloorIcons(floor._id);
        setIcons(iconsData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching floorplan icons:', err);
        setError('Failed to load floorplan icons. Please try again later.');
        setLoading(false);
      }
    };
    
    fetchIcons();
  }, [floor._id]);
  
  // Handle zoom change
  const handleZoomChange = (event, newValue) => {
    setZoom(newValue);
  };
  
  // Handle icon click
  const handleIconClick = (icon, event) => {
    event.stopPropagation();
    setSelectedIcon(icon);
  };
  
  // Handle icon drag start
  const handleIconDragStart = (icon, event) => {
    event.stopPropagation();
    
    if (isPanning) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const offsetX = event.clientX - rect.left;
    const offsetY = event.clientY - rect.top;
    
    setDraggedIcon(icon);
    setDragOffset({ x: offsetX, y: offsetY });
    setIsDragging(true);
    setUnsavedChanges(true);
  };
  
  // Handle mouse move for dragging
  const handleMouseMove = (event) => {
    if (isDragging && draggedIcon) {
      const containerRect = containerRef.current.getBoundingClientRect();
      const floorplanRect = floorplanRef.current.getBoundingClientRect();
      
      // Calculate position relative to the floorplan
      const x = (event.clientX - floorplanRect.left - dragOffset.x) / zoom;
      const y = (event.clientY - floorplanRect.top - dragOffset.y) / zoom;
      
      // Update the icon's position
      setIcons(prevIcons => 
        prevIcons.map(icon => 
          icon._id === draggedIcon._id 
            ? { ...icon, position: { ...icon.position, x, y } } 
            : icon
        )
      );
    } else if (isPanning) {
      const dx = event.clientX - panStart.x;
      const dy = event.clientY - panStart.y;
      
      setViewPosition(prev => ({
        x: prev.x + dx,
        y: prev.y + dy
      }));
      
      setPanStart({
        x: event.clientX,
        y: event.clientY
      });
    }
  };
  
  // Handle mouse up for dragging
  const handleMouseUp = () => {
    if (isDragging) {
      setIsDragging(false);
      setDraggedIcon(null);
    }
    
    if (isPanning) {
      setIsPanning(false);
    }
  };
  
  // Handle pan start
  const handlePanStart = (event) => {
    if (event.button === 0 && !isDragging) { // Left mouse button
      setIsPanning(true);
      setPanStart({
        x: event.clientX,
        y: event.clientY
      });
      event.preventDefault();
    }
  };
  
  // Handle floorplan click for adding new icon
  const handleFloorplanClick = (event) => {
    if (isDragging || isPanning) return;
    
    const rect = floorplanRef.current.getBoundingClientRect();
    const x = (event.clientX - rect.left) / zoom;
    const y = (event.clientY - rect.top) / zoom;
    
    // Reset form data
    setIconFormData({
      name: '',
      type: 'temperature',
      customType: '',
      color: '#1976d2',
      status: 'normal',
      data: {
        value: '',
        unit: '',
        min: '',
        max: ''
      },
      deviceId: '',
      integrationSource: '',
      metadata: {
        description: '',
        room: '',
        notes: ''
      }
    });
    
    // Set position for new icon
    setIconFormData(prev => ({
      ...prev,
      position: { x, y, rotation: 0 },
      size: { width: 32, height: 32 }
    }));
    
    setSelectedIcon(null);
    setIconDialogOpen(true);
  };
  
  // Open icon dialog for editing
  const handleEditIcon = (icon) => {
    setSelectedIcon(icon);
    
    setIconFormData({
      name: icon.name,
      type: icon.type,
      customType: icon.customType || '',
      color: icon.color || '#1976d2',
      status: icon.status || 'normal',
      position: icon.position,
      size: icon.size,
      data: {
        value: icon.data?.value || '',
        unit: icon.data?.unit || '',
        min: icon.data?.min || '',
        max: icon.data?.max || ''
      },
      deviceId: icon.deviceId || '',
      integrationSource: icon.integrationSource || '',
      metadata: {
        description: icon.metadata?.description || '',
        room: icon.metadata?.room || '',
        notes: icon.metadata?.notes || ''
      }
    });
    
    setIconDialogOpen(true);
  };
  
  // Close icon dialog
  const handleCloseIconDialog = () => {
    setIconDialogOpen(false);
  };
  
  // Handle form input changes
  const handleFormChange = (e) => {
    const { name, value } = e.target;
    
    // Handle nested objects (data, metadata)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setIconFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setIconFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
  };
  
  // Save icon
  const handleSaveIcon = async () => {
    try {
      // Process form data
      const iconData = {
        ...iconFormData,
        floorId: floor._id
      };
      
      if (selectedIcon) {
        // Update existing icon
        await buildingManagementService.updateFloorplanIcon(selectedIcon._id, iconData);
      } else {
        // Create new icon
        await buildingManagementService.createFloorplanIcon(iconData);
      }
      
      // Refresh icons
      const iconsData = await buildingManagementService.getFloorIcons(floor._id);
      setIcons(iconsData);
      
      setIconDialogOpen(false);
      setUnsavedChanges(false);
    } catch (err) {
      console.error('Error saving floorplan icon:', err);
      setError('Failed to save floorplan icon. Please try again later.');
    }
  };
  
  // Delete icon
  const handleDeleteIcon = async (iconId) => {
    if (window.confirm('Are you sure you want to delete this icon?')) {
      try {
        await buildingManagementService.deleteFloorplanIcon(iconId);
        
        // Refresh icons
        const iconsData = await buildingManagementService.getFloorIcons(floor._id);
        setIcons(iconsData);
        
        setSelectedIcon(null);
        setUnsavedChanges(false);
      } catch (err) {
        console.error('Error deleting floorplan icon:', err);
        setError('Failed to delete floorplan icon. Please try again later.');
      }
    }
  };
  
  // Save all icon positions
  const handleSavePositions = async () => {
    try {
      // Only send the necessary data for position updates
      const positionUpdates = icons.map(icon => ({
        _id: icon._id,
        position: icon.position
      }));
      
      await buildingManagementService.bulkUpdateFloorplanIconPositions(positionUpdates);
      setUnsavedChanges(false);
    } catch (err) {
      console.error('Error saving icon positions:', err);
      setError('Failed to save icon positions. Please try again later.');
    }
  };
  
  // Render icon based on type
  const renderIcon = (icon) => {
    const IconComponent = iconTypeMap[icon.type] || CustomIcon;
    
    return (
      <Box
        key={icon._id}
        sx={{
          position: 'absolute',
          left: `${icon.position.x}px`,
          top: `${icon.position.y}px`,
          transform: `rotate(${icon.position.rotation || 0}deg)`,
          width: `${icon.size?.width || 32}px`,
          height: `${icon.size?.height || 32}px`,
          color: icon.color || '#1976d2',
          cursor: 'move',
          zIndex: selectedIcon && selectedIcon._id === icon._id ? 10 : 1,
          transition: isDragging && draggedIcon && draggedIcon._id === icon._id ? 'none' : 'all 0.2s ease',
          '&:hover': {
            filter: 'brightness(1.2)'
          }
        }}
        onMouseDown={(e) => handleIconDragStart(icon, e)}
        onClick={(e) => handleIconClick(icon, e)}
      >
        <IconComponent 
          sx={{ 
            fontSize: `${icon.size?.width || 32}px`,
            color: icon.status === 'warning' ? 'orange' : 
                  icon.status === 'alert' ? 'red' : 
                  icon.status === 'inactive' ? 'gray' : 
                  icon.color || '#1976d2'
          }} 
        />
      </Box>
    );
  };
  
  const handle3DEditorSave = async (floorplan3DData) => {
    try {
      await buildingManagementService.updateFloor3DPlan(floor._id, floorplan3DData);
      setUnsavedChanges(false);
      setError(null);
    } catch (err) {
      console.error('Error saving 3D floorplan:', err);
      setError('Failed to save 3D floorplan. Please try again later.');
    }
  };

  if (show3DEditor) {
    return (
      <Suspense fallback={
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh' }}>
          <CircularProgress />
        </Box>
      }>
        <FloorPlan3DEditor 
          floor={floor}
          onSave={handle3DEditorSave}
          onClose={() => setShow3DEditor(false)}
        />
      </Suspense>
    );
  }

  return (
    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
      {/* Toolbar */}
      <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <ToggleButtonGroup
            value={editorMode}
            exclusive
            onChange={(e, newMode) => {
              if (newMode === '3d') {
                setShow3DEditor(true);
              } else if (newMode) {
                setEditorMode(newMode);
              }
            }}
            size="small"
          >
            <ToggleButton value="icons">
              <Tooltip title="Icon Editor">
                <LocationOnIcon />
              </Tooltip>
            </ToggleButton>
            <ToggleButton value="3d">
              <Tooltip title="3D Floor Plan Editor">
                <View3DIcon />
              </Tooltip>
            </ToggleButton>
          </ToggleButtonGroup>
          
          <Divider orientation="vertical" flexItem />
          
          <Typography variant="subtitle1" sx={{ mr: 2 }}>Zoom:</Typography>
          <Slider
            value={zoom}
            onChange={handleZoomChange}
            min={0.5}
            max={2}
            step={0.1}
            valueLabelDisplay="auto"
            valueLabelFormat={(value) => `${Math.round(value * 100)}%`}
            sx={{ width: 150, mr: 2 }}
          />
          <Tooltip title="Pan Tool">
            <IconButton 
              color={isPanning ? "primary" : "default"}
              onClick={() => setIsPanning(!isPanning)}
            >
              <PanIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom In">
            <IconButton onClick={() => setZoom(prev => Math.min(prev + 0.1, 2))}>
              <ZoomInIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Zoom Out">
            <IconButton onClick={() => setZoom(prev => Math.max(prev - 0.1, 0.5))}>
              <ZoomOutIcon />
            </IconButton>
          </Tooltip>
        </Box>
        <Box>
          <Button 
            variant="contained" 
            color="primary" 
            startIcon={<SaveIcon />}
            onClick={handleSavePositions}
            disabled={!unsavedChanges}
          >
            Save Positions
          </Button>
        </Box>
      </Box>
      
      <Grid container spacing={2} sx={{ flexGrow: 1 }}>
        {/* Floorplan Area */}
        <Grid item xs={9}>
          <Paper 
            ref={containerRef}
            sx={{ 
              height: '100%', 
              overflow: 'hidden', 
              position: 'relative',
              cursor: isPanning ? 'grab' : 'default'
            }}
            onMouseDown={isPanning ? handlePanStart : null}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                <CircularProgress />
              </Box>
            ) : error ? (
              <Box sx={{ p: 3 }}>
                <Alert severity="error">{error}</Alert>
              </Box>
            ) : (
              <Box 
                ref={floorplanRef}
                onClick={handleFloorplanClick}
                sx={{ 
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: `translate(calc(-50% + ${viewPosition.x}px), calc(-50% + ${viewPosition.y}px)) scale(${zoom})`,
                  transformOrigin: 'center',
                  width: '100%',
                  height: '100%',
                  backgroundImage: `url(${buildingManagementService.getFloorplanUrl(floor._id)})`,
                  backgroundSize: 'contain',
                  backgroundPosition: 'center',
                  backgroundRepeat: 'no-repeat'
                }}
              >
                {icons.map(icon => renderIcon(icon))}
              </Box>
            )}
          </Paper>
        </Grid>
        
        {/* Icons List */}
        <Grid item xs={3}>
          <Paper sx={{ height: '100%', overflow: 'auto', p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Icons</Typography>
              <Button 
                startIcon={<AddIcon />} 
                size="small"
                onClick={() => {
                  setSelectedIcon(null);
                  setIconDialogOpen(true);
                }}
              >
                Add Icon
              </Button>
            </Box>
            <Divider sx={{ mb: 2 }} />
            
            {icons.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', mt: 2 }}>
                No icons yet. Click on the floorplan to add an icon.
              </Typography>
            ) : (
              <List>
                {icons.map(icon => {
                  const IconComponent = iconTypeMap[icon.type] || CustomIcon;
                  
                  return (
                    <ListItem 
                      key={icon._id}
                      selected={selectedIcon && selectedIcon._id === icon._id}
                      onClick={() => setSelectedIcon(icon)}
                      sx={{ 
                        cursor: 'pointer',
                        '&:hover': { backgroundColor: 'rgba(0, 0, 0, 0.04)' }
                      }}
                    >
                      <ListItemIcon>
                        <IconComponent sx={{ 
                          color: icon.status === 'warning' ? 'orange' : 
                                icon.status === 'alert' ? 'red' : 
                                icon.status === 'inactive' ? 'gray' : 
                                icon.color || '#1976d2'
                        }} />
                      </ListItemIcon>
                      <ListItemText 
                        primary={icon.name} 
                        secondary={
                          <>
                            {icon.type}
                            {icon.data?.value && (
                              <Chip 
                                size="small" 
                                label={`${icon.data.value}${icon.data.unit || ''}`} 
                                sx={{ ml: 1 }}
                              />
                            )}
                          </>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton 
                          edge="end" 
                          size="small"
                          onClick={() => handleEditIcon(icon)}
                        >
                          <EditIcon />
                        </IconButton>
                        <IconButton 
                          edge="end" 
                          size="small"
                          onClick={() => handleDeleteIcon(icon._id)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  );
                })}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
      
      {/* Icon Dialog */}
      <Dialog open={iconDialogOpen} onClose={handleCloseIconDialog} maxWidth="md" fullWidth>
        <DialogTitle>{selectedIcon ? 'Edit Icon' : 'Add Icon'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            {/* Basic Information */}
            <Grid item xs={12}>
              <Typography variant="h6">Basic Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                required
                fullWidth
                label="Icon Name"
                name="name"
                value={iconFormData.name}
                onChange={handleFormChange}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Icon Type</InputLabel>
                <Select
                  name="type"
                  value={iconFormData.type}
                  onChange={handleFormChange}
                  label="Icon Type"
                >
                  <MenuItem value="temperature">Temperature</MenuItem>
                  <MenuItem value="door">Door</MenuItem>
                  <MenuItem value="camera">Camera</MenuItem>
                  <MenuItem value="motion">Motion Sensor</MenuItem>
                  <MenuItem value="light">Light</MenuItem>
                  <MenuItem value="hvac">HVAC</MenuItem>
                  <MenuItem value="fire">Fire Alarm</MenuItem>
                  <MenuItem value="water">Water</MenuItem>
                  <MenuItem value="power">Power</MenuItem>
                  <MenuItem value="network">Network</MenuItem>
                  <MenuItem value="custom">Custom</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {iconFormData.type === 'custom' && (
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Custom Type Name"
                  name="customType"
                  value={iconFormData.customType}
                  onChange={handleFormChange}
                />
              </Grid>
            )}
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Color"
                name="color"
                value={iconFormData.color}
                onChange={handleFormChange}
                type="color"
                InputProps={{
                  startAdornment: (
                    <Box 
                      sx={{ 
                        width: 20, 
                        height: 20, 
                        backgroundColor: iconFormData.color,
                        marginRight: 1,
                        border: '1px solid #ccc'
                      }} 
                    />
                  ),
                }}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  name="status"
                  value={iconFormData.status}
                  onChange={handleFormChange}
                  label="Status"
                >
                  <MenuItem value="normal">Normal</MenuItem>
                  <MenuItem value="warning">Warning</MenuItem>
                  <MenuItem value="alert">Alert</MenuItem>
                  <MenuItem value="inactive">Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            {/* Data Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2 }}>Data Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Value"
                name="data.value"
                value={iconFormData.data.value}
                onChange={handleFormChange}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Unit"
                name="data.unit"
                value={iconFormData.data.unit}
                onChange={handleFormChange}
                placeholder="e.g., °F, %, on/off"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Minimum Value"
                name="data.min"
                type="number"
                value={iconFormData.data.min}
                onChange={handleFormChange}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Maximum Value"
                name="data.max"
                type="number"
                value={iconFormData.data.max}
                onChange={handleFormChange}
              />
            </Grid>
            
            {/* Integration Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2 }}>Integration Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Device ID"
                name="deviceId"
                value={iconFormData.deviceId}
                onChange={handleFormChange}
                placeholder="ID of the physical device"
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Integration Source"
                name="integrationSource"
                value={iconFormData.integrationSource}
                onChange={handleFormChange}
                placeholder="e.g., dreo, unifiAccess"
              />
            </Grid>
            
            {/* Additional Information */}
            <Grid item xs={12}>
              <Typography variant="h6" sx={{ mt: 2 }}>Additional Information</Typography>
              <Divider sx={{ mb: 2 }} />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Description"
                name="metadata.description"
                value={iconFormData.metadata.description}
                onChange={handleFormChange}
              />
            </Grid>
            
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Room"
                name="metadata.room"
                value={iconFormData.metadata.room}
                onChange={handleFormChange}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Notes"
                name="metadata.notes"
                value={iconFormData.metadata.notes}
                onChange={handleFormChange}
                multiline
                rows={2}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseIconDialog}>Cancel</Button>
          <Button onClick={handleSaveIcon} variant="contained" color="primary">
            {selectedIcon ? 'Update Icon' : 'Add Icon'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FloorplanEditor;