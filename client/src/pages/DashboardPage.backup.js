import React, { useState, useCallback, useRef } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Button,
  Divider,
  Paper,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  TextField
} from '@mui/material';
import { 
  Add as AddIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Close as CloseIcon,
  DragIndicator as DragIndicatorIcon,
  AspectRatio as ResizeIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useAuth } from '../context/AuthContext';
import { useDashboard } from '../context/DashboardContext';
import { renderWidget, widgetTypes } from '../components/widgets/WidgetRegistry';
import WidgetSettingsForm from '../components/widgets/WidgetSettingsForm';
import { debounce } from '../utils/performance';

// Calculate widget width based breakpoints for responsive behavior
const getWidgetBreakpoints = (widgetGridWidth, containerWidth = 1200) => {
  // Calculate the actual pixel width based on grid position
  const actualWidth = (widgetGridWidth / 12) * containerWidth;
  
  // Define breakpoints based on actual widget width instead of screen width
  return {
    xs: actualWidth < 300,     // Very small widgets
    sm: actualWidth >= 300 && actualWidth < 500,    // Small widgets
    md: actualWidth >= 500 && actualWidth < 700,    // Medium widgets
    lg: actualWidth >= 700 && actualWidth < 900,    // Large widgets
    xl: actualWidth >= 900     // Extra large widgets
  };
};

// CSS for responsive widget sizing - supports 25%, 50%, 75%, 100%
const widgetStyles = `
  @media (min-width: 1536px) {
    .widget-container.widget-md-12 { width: calc(100% - 24px) !important; }
    .widget-container.widget-md-9 { width: calc(75% - 24px) !important; }
    .widget-container.widget-md-8 { width: calc(66.666% - 24px) !important; }
    .widget-container.widget-md-6 { width: calc(50% - 24px) !important; }
    .widget-container.widget-md-4 { width: calc(33.333% - 24px) !important; }
    .widget-container.widget-md-3 { width: calc(25% - 24px) !important; }
  }
  
  @media (min-width: 1200px) and (max-width: 1535px) {
    .widget-container.widget-md-12 { width: calc(100% - 24px) !important; }
    .widget-container.widget-md-9 { width: calc(75% - 24px) !important; }
    .widget-container.widget-md-8 { width: calc(66.666% - 24px) !important; }
    .widget-container.widget-md-6 { width: calc(50% - 24px) !important; }
    .widget-container.widget-md-4 { width: calc(33.333% - 24px) !important; }
    .widget-container.widget-md-3 { width: calc(25% - 24px) !important; }
  }
  
  @media (min-width: 960px) and (max-width: 1199px) {
    .widget-container.widget-md-12 { width: calc(100% - 24px) !important; }
    .widget-container.widget-md-9 { width: calc(100% - 24px) !important; }
    .widget-container.widget-md-8 { width: calc(100% - 24px) !important; }
    .widget-container.widget-md-6 { width: calc(50% - 24px) !important; }
    .widget-container.widget-md-4 { width: calc(50% - 24px) !important; }
    .widget-container.widget-md-3 { width: calc(33.333% - 24px) !important; }
  }
  
  @media (min-width: 600px) and (max-width: 959px) {
    .widget-container.widget-sm-12 { width: calc(100% - 24px) !important; }
    .widget-container.widget-sm-6 { width: calc(50% - 24px) !important; }
    .widget-container.widget-sm-4 { width: calc(33.333% - 24px) !important; }
    .widget-container.widget-sm-3 { width: calc(25% - 24px) !important; }
  }
  
  @media (max-width: 599px) {
    .widget-container { width: calc(100% - 24px) !important; }
  }

  /* Drop zone highlighting styles */
  .drop-zone-highlight {
    border: 2px dashed #0066ff !important;
    background: rgba(0, 102, 255, 0.1) !important;
    border-radius: 12px;
    min-height: 150px;
    transition: all 0.3s ease;
  }
  
  .drop-zone-25 { width: calc(25% - 24px) !important; }
  .drop-zone-50 { width: calc(50% - 24px) !important; }
  .drop-zone-75 { width: calc(75% - 24px) !important; }
  .drop-zone-100 { width: calc(100% - 24px) !important; }
`;

const DashboardPage = () => {
  const { user, hasPermission } = useAuth();
  const { 
    preferences, 
    unsavedPreferences,
    loading, 
    error, 
    isEditMode, 
    hasUnsavedChanges,
    toggleEditMode, 
    addWidget, 
    updateWidget, 
    removeWidget, 
    resetDashboard, 
    updateLayout: originalUpdateLayout,
    saveDashboardChanges,
    discardChanges
  } = useDashboard();

  // Track if a resize operation is in progress
  const [isResizing, setIsResizing] = useState(false);

  // Create a debounced function to reset the resizing flag
  const debouncedResetResizing = useCallback(
    debounce(() => {
      setIsResizing(false);
    }, 500),
    []
  );

  // State for add widget dialog
  const [addWidgetOpen, setAddWidgetOpen] = useState(false);

  // State for widget settings dialog
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [currentWidget, setCurrentWidget] = useState(null);

  // State for dashboard menu
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const menuOpen = Boolean(menuAnchorEl);

  // Handle opening the add widget dialog
  const handleAddWidgetOpen = () => {
    setAddWidgetOpen(true);
  };

  // Handle closing the add widget dialog
  const handleAddWidgetClose = () => {
    setAddWidgetOpen(false);
  };

  // Handle opening the settings dialog
  const handleSettingsOpen = (widget) => {
    setCurrentWidget(widget);
    setWidgetTitle(widget.title || '');
    setWidgetFormSettings(widget.settings || {});
    setSettingsOpen(true);
  };

  // Handle closing the settings dialog
  const handleSettingsClose = () => {
    setSettingsOpen(false);
    setCurrentWidget(null);
    setWidgetTitle('');
    setWidgetFormSettings({});
  };

  // Handle opening the dashboard menu
  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  // Handle closing the dashboard menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle adding a new widget
  const handleAddWidget = (type) => {
    const widgetType = widgetTypes.find(w => w.type === type);
    if (!widgetType) return;

    // Determine default size based on widget type - optimized for 4 small per row
    let defaultSize = { w: 3, h: 2 }; // Small size by default (25% width for 4 per row)
    
    // Special handling for specific widget types
    if (type === 'shortcuts' || type === 'recentFiles') {
      defaultSize = { w: 6, h: 2 }; // Large width (50% for 2 per row)
    } else if (type === 'calendar' || type === 'weather') {
      defaultSize = { w: 3, h: 2 }; // Small width (25% for 4 per row)
    } else if (type === 'notes' || type === 'tasks') {
      defaultSize = { w: 6, h: 3 }; // Large width, taller (50% for 2 per row)
    }

    // Add the widget with default position (will be placed at the end by the flex layout)
    addWidget(
      type, 
      widgetType.title, 
      { x: 0, y: 0, ...defaultSize }
    );

    handleAddWidgetClose();
  };

  // Handle removing a widget
  const handleRemoveWidget = (widgetId) => {
    if (window.confirm('Are you sure you want to remove this widget?')) {
      removeWidget(widgetId);
    }
  };

  // Handle editing a widget
  const handleEditWidget = (widgetId) => {
    // Use unsavedPreferences when in edit mode, otherwise use preferences
    const currentPreferences = isEditMode && unsavedPreferences ? unsavedPreferences : preferences;
    const widget = currentPreferences?.widgets?.find(w => w._id === widgetId);
    if (widget) {
      handleSettingsOpen(widget);
    }
  };

  // State for widget settings form
  const [widgetFormSettings, setWidgetFormSettings] = useState({});
  const [widgetTitle, setWidgetTitle] = useState('');

  // Handle saving widget settings
  const handleSaveSettings = () => {
    if (!currentWidget) return;

    updateWidget(currentWidget._id, {
      title: widgetTitle || currentWidget.title,
      settings: widgetFormSettings
    });

    handleSettingsClose();
  };
  
  // Handle widget resize - cycle through 25%, 50%, 75%, 100%
  const handleWidgetResize = (widgetId) => {
    // Only allow changes in edit mode and if widgets exist
    if (!isEditMode || !unsavedPreferences?.widgets) return;
    
    // Set resizing flag to true to prevent loading indicator during resize
    setIsResizing(true);
    
    // Find the widget to resize
    const widget = unsavedPreferences.widgets.find(w => w._id.toString() === widgetId.toString());
    if (!widget) return;
    
    // Get current size based on width
    let currentSize = 'small'; // Default
    if (widget.position.w >= 12) {
      currentSize = 'full';      // 100% width
    } else if (widget.position.w >= 9) {
      currentSize = 'large';     // 75% width
    } else if (widget.position.w >= 6) {
      currentSize = 'medium';    // 50% width
    } else {
      currentSize = 'small';     // 25% width
    }
    
    // Determine next size in the cycle: 25% -> 50% -> 75% -> 100% -> 25%
    let nextWidth = 3;
    let nextHeight = 2;
    
    switch (currentSize) {
      case 'small':   // 25% -> 50%
        nextWidth = 6;
        nextHeight = 2;
        break;
      case 'medium':  // 50% -> 75%
        nextWidth = 9;
        nextHeight = 3;
        break;
      case 'large':   // 75% -> 100%
        nextWidth = 12;
        nextHeight = 3;
        break;
      case 'full':    // 100% -> 25%
        nextWidth = 3;
        nextHeight = 2;
        break;
      default:
        nextWidth = 6;
        nextHeight = 2;
    }
    
    // Use the updateWidget function from context to properly update the widget
    updateWidget(widgetId, {
      position: {
        ...widget.position,
        w: nextWidth,
        h: nextHeight
      }
    });
    
    // Reset the resizing flag after a delay
    debouncedResetResizing();
  };

  // Handle drag end event from react-beautiful-dnd
  const handleDragEnd = (result) => {
    // Only allow changes in edit mode and if widgets exist
    if (!isEditMode || !unsavedPreferences?.widgets || !result.destination) return;

    // Set resizing flag to true to prevent loading indicator during drag
    setIsResizing(true);

    // Get the source and destination indices
    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    // If dropped in the same position, do nothing
    if (sourceIndex === destinationIndex) {
      setIsResizing(false);
      return;
    }

    // Update the order of widgets - create new array with reordered widgets
    const updatedWidgets = [...unsavedPreferences.widgets];
    
    // Remove the widget from its current position
    const [movedWidget] = updatedWidgets.splice(sourceIndex, 1);
    
    // Insert it at the new position
    updatedWidgets.splice(destinationIndex, 0, movedWidget);
    
    // Recalculate positions for all widgets using an intelligent grid layout
    let xPos = 0;
    let yPos = 0;
    const GRID_COLS = 12; // Using 12-column grid system
    
    // Update widget positions in the reordered array
    const widgetsWithUpdatedPositions = updatedWidgets.map((widget, index) => {
      const widgetWidth = widget.position?.w || 3;
      
      // Check if widget fits in current row, if not move to next row
      if (xPos + widgetWidth > GRID_COLS) {
        xPos = 0;
        yPos++;
      }
      
      // Create updated widget with new position
      const updatedWidget = {
        ...widget,
        position: {
          ...widget.position,
          x: xPos,
          y: yPos,
          w: widgetWidth,
          h: widget.position?.h || 2
        }
      };
      
      // Update x position for next widget
      xPos += widgetWidth;
      if (xPos >= GRID_COLS) {
        xPos = 0;
        yPos++;
      }
      
      return updatedWidget;
    });
    
    // Build a layout array and delegate state updates to context updateLayout
    const updatedLayout = widgetsWithUpdatedPositions.map(widget => ({
      i: widget._id.toString(),
      x: widget.position.x,
      y: widget.position.y,
      w: widget.position.w || 4,
      h: widget.position.h || 2
    }));

    // Update layout via context to track unsaved changes properly
    originalUpdateLayout(updatedLayout);
    
    // Reset the resizing flag after a delay
    debouncedResetResizing();
  };

  // Legacy method for compatibility
  const handleWidgetOrderChange = (widgetId, newOrder) => {
    // Only allow changes in edit mode and if widgets exist
    if (!isEditMode || !unsavedPreferences?.widgets) return;

    // Set resizing flag to true to prevent loading indicator during resize
    setIsResizing(true);

    // Update the order of widgets
    const updatedWidgets = [...unsavedPreferences.widgets];
    
    // Find the widget to move
    const widgetIndex = updatedWidgets.findIndex(w => w._id === widgetId);
    if (widgetIndex === -1) return;
    
    // Remove the widget from its current position
    const [widget] = updatedWidgets.splice(widgetIndex, 1);
    
    // Insert it at the new position
    updatedWidgets.splice(newOrder, 0, widget);
    
    // Update positions for all widgets
    const updatedLayout = updatedWidgets.map((widget, index) => ({
      i: widget._id.toString(),
      x: index % 3, // Simple grid layout: 3 columns
      y: Math.floor(index / 3),
      w: widget.position.w || 4,
      h: widget.position.h || 2
    }));

    // Update layout immediately to track changes
    originalUpdateLayout(updatedLayout);
    
    // Reset the resizing flag after a delay to prevent loading indicator during resize
    debouncedResetResizing();
  };

  // Handle saving dashboard changes
  const handleSaveDashboard = async () => {
    if (hasUnsavedChanges) {
      try {
        await saveDashboardChanges();
        // Pass true to toggleEditMode to skip saving again
        toggleEditMode(true);
      } catch (err) {
        console.error('Error saving dashboard:', err);
        // You might want to show an error message to the user here
        return; // Don't exit edit mode if saving fails
      }
    } else {
      // If no unsaved changes, just toggle edit mode
      toggleEditMode();
    }
  };

  // Handle resetting the dashboard
  const handleResetDashboard = () => {
    if (window.confirm('Are you sure you want to reset your dashboard to default?')) {
      resetDashboard();
      handleMenuClose();
    }
  };

  // Determine grid size for each widget based on its size
  const getWidgetGridSize = (widget) => {
    // Default sizes
    let xs = 12; // Full width on mobile
    let sm = 6;  // Half width on tablets
    let md = 4;  // One-third width on desktops
    
    // Ensure widget.position exists
    const position = widget.position || { w: 4, h: 2 };
    
    // Special handling for specific widget types
    if (widget.type === 'shortcuts' || widget.type === 'recentFiles') {
      // These widgets are always half width on desktop and tablet, full width on mobile
      return { xs: 12, sm: 6, md: 6 };
    }
    
    // Size based on widget's position.w property - supports 25%, 50%, 75%, 100%
    if (position.w >= 12) {
      // Full width widgets (100%)
      md = 12; // Full width on desktop
      sm = 12; // Full width on tablet
    } else if (position.w >= 9) {
      // Large widgets (75% width)
      md = 9; // Three-fourths width on desktop
      sm = 12; // Full width on tablet
    } else if (position.w >= 6) {
      // Medium widgets (50% width) - 2 per row
      md = 6; // Half width on desktop (2 per row)
      sm = 12; // Full width on tablet
    } else if (position.w >= 4) {
      // Small-medium widgets (33% width)
      md = 4; // One-third width on desktop (3 per row)
      sm = 6; // Half width on tablet
    } else {
      // Small widgets (25% width) - 4 per row
      md = 3; // One-fourth width on desktop (4 per row)
      sm = 6; // Half width on tablet
    }
    
    return { xs, sm, md };
  };

  // Permission helper: check if current user can access a given widget type
  const canUseWidgetType = (type) => {
    const def = widgetTypes.find(w => w.type === type);
    const required = def && def.requiredPermission;
    // If no required permission specified, default to allowed
    if (!required) return true;
    // Use AuthContext hasPermission
    return typeof hasPermission === 'function' ? hasPermission(required) : true;
  };

  // Filter widgets in preferences based on permissions
  const filterWidgetsByPermission = (widgets) => {
    if (!Array.isArray(widgets)) return [];
    return widgets.filter((w) => canUseWidgetType(w.type));
  };

  const displayedWidgets = (isEditMode ? filterWidgetsByPermission(unsavedPreferences?.widgets) : filterWidgetsByPermission(preferences?.widgets));

  return (
    <Container maxWidth="xl">
      {/* Add style tag for responsive widget sizing */}
      <style dangerouslySetInnerHTML={{ __html: widgetStyles }} />
      
      <Box sx={{ 
        mb: 4, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        borderRadius: '12px',
        p: 3,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
      }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom sx={{ color: '#2d3748', fontWeight: 700 }}>
            Welcome, {user?.name}
          </Typography>
          <Typography variant="body1" sx={{ color: '#718096', fontSize: '1.1rem' }}>
            Here's your dashboard with quick access to important resources
          </Typography>
        </Box>
        <Box>
          <Tooltip title={isEditMode ? "Save Layout" : "Customize Dashboard"}>
            <IconButton 
              color={isEditMode ? "primary" : "default"} 
              onClick={isEditMode ? handleSaveDashboard : () => toggleEditMode()}
              sx={{ mr: 1 }}
            >
              {isEditMode ? <SaveIcon /> : <EditIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Dashboard Options">
            <IconButton
              onClick={handleMenuOpen}
              aria-controls={menuOpen ? 'dashboard-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={menuOpen ? 'true' : undefined}
            >
              <DashboardIcon />
            </IconButton>
          </Tooltip>
          <Menu
            id="dashboard-menu"
            anchorEl={menuAnchorEl}
            open={menuOpen}
            onClose={handleMenuClose}
            MenuListProps={{
              'aria-labelledby': 'dashboard-menu-button',
            }}
          >
            <MenuItem onClick={handleResetDashboard}>
              <ListItemIcon>
                <RefreshIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Reset Dashboard</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {loading && !isResizing ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : displayedWidgets.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Your dashboard is empty
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Add widgets to customize your dashboard
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleAddWidgetOpen}
          >
            Add Widget
          </Button>
        </Paper>
      ) : (
        <Box sx={{ position: 'relative', pb: 8 }}>
          {isEditMode ? (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="dashboard-widgets" type="WIDGET" direction="horizontal">
                {(provided, snapshot) => {
                  // Get the dragged widget width for drop zone highlighting
                  const draggedWidget = snapshot.draggingFromThisWith ? 
                    unsavedPreferences?.widgets?.find(w => w._id.toString() === snapshot.draggingFromThisWith) : null;
                  const draggedWidgetWidth = draggedWidget?.position?.w || 3;
                  const widthClass = draggedWidgetWidth >= 12 ? 'drop-zone-100' : 
                                   draggedWidgetWidth >= 9 ? 'drop-zone-75' : 
                                   draggedWidgetWidth >= 6 ? 'drop-zone-50' : 'drop-zone-25';
                  
                  return (
                    <div
                      {...provided.droppableProps}
                      ref={provided.innerRef}
                      className={snapshot.isDraggingOver ? `drop-zone-highlight ${widthClass}` : ''}
                      style={{ 
                        display: 'flex', 
                        flexWrap: 'wrap', 
                        margin: '-12px',
                        minHeight: '200px',
                        borderRadius: '12px',
                        border: snapshot.isDraggingOver ? '2px dashed #0066ff' : '2px dashed transparent',
                        backgroundColor: snapshot.isDraggingOver ? 'rgba(0, 102, 255, 0.05)' : 'transparent',
                        transition: 'all 0.3s ease'
                      }}
                    >
                    {filterWidgetsByPermission(unsavedPreferences?.widgets)?.map((widget, index) => {
                      const gridSize = getWidgetGridSize(widget);
                      
                      return (
                        <Draggable 
                          key={widget._id.toString()} 
                          draggableId={widget._id.toString()} 
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              style={{
                                ...provided.draggableProps.style,
                                width: `calc(${100 * (gridSize.md / 12)}% - 24px)`,
                                padding: '12px',
                                boxSizing: 'border-box'
                              }}
                              className={`widget-container 
                                widget-md-${gridSize.md} 
                                widget-sm-${gridSize.sm} 
                                widget-xs-${gridSize.xs}`}
                            >
                              <Paper 
                                sx={{ 
                                  p: 2, 
                                  height: '100%',
                                  position: 'relative',
                                  transition: 'all 0.3s ease',
                                  boxShadow: snapshot.isDragging ? '0 12px 40px rgba(0, 102, 255, 0.3)' : '0 4px 20px rgba(0, 0, 0, 0.1)',
                                  background: snapshot.isDragging ? 'linear-gradient(135deg, rgba(0, 102, 255, 0.1), rgba(0, 82, 204, 0.1))' : 'rgba(255, 255, 255, 0.95)',
                                  backdropFilter: 'blur(10px)',
                                  borderRadius: '12px',
                                  border: snapshot.isDragging ? '2px solid #0066ff' : '1px solid rgba(0, 0, 0, 0.1)',
                                  transform: snapshot.isDragging ? 'rotate(2deg) scale(1.05)' : 'none',
                                  '&:hover': {
                                    boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
                                    transform: snapshot.isDragging ? 'rotate(2deg) scale(1.05)' : 'translateY(-2px)',
                                    border: '2px solid #0066ff',
                                    '& .widget-controls': {
                                      opacity: 1
                                    },
                                    '& .resize-handles': {
                                      opacity: 1
                                    }
                                  },
                                  // Resize handles (visual indicators)
                                  '& .resize-handles': {
                                    opacity: 0,
                                    transition: 'opacity 0.2s ease',
                                    '&::before': {
                                      content: '""',
                                      position: 'absolute',
                                      top: -2,
                                      right: -2,
                                      width: 12,
                                      height: 12,
                                      background: '#0066ff',
                                      borderRadius: '50%',
                                      cursor: 'se-resize',
                                      zIndex: 20
                                    },
                                    '&::after': {
                                      content: '""',
                                      position: 'absolute',
                                      bottom: -2,
                                      right: -2,
                                      width: 0,
                                      height: 0,
                                      borderLeft: '8px solid transparent',
                                      borderTop: '8px solid #0066ff',
                                      cursor: 'se-resize',
                                      zIndex: 20
                                    }
                                  }
                                }}
                              >
                                <Box sx={{ position: 'relative' }}>
                                  {/* Invisible drag handle covering entire widget */}
                                  <div 
                                    {...provided.dragHandleProps}
                                    style={{
                                      position: 'absolute',
                                      top: 0,
                                      left: 0,
                                      right: 0,
                                      bottom: 0,
                                      zIndex: 1,
                                      cursor: 'grab'
                                    }}
                                  />
                                  
                                  <Box 
                                    className="widget-controls"
                                    sx={{ 
                                      position: 'absolute', 
                                      top: 0, 
                                      right: 0, 
                                      zIndex: 10,
                                      display: 'flex',
                                      opacity: 0,
                                      transition: 'opacity 0.2s ease'
                                    }}
                                  >
                                    <IconButton size="small" title="Drag to move" disabled>
                                      <DragIndicatorIcon fontSize="small" />
                                    </IconButton>
                                    <IconButton 
                                      size="small" 
                                      onClick={() => handleWidgetResize(widget._id)}
                                      title="Toggle size (25% → 50% → 25%)"
                                    >
                                      <ResizeIcon fontSize="small" />
                                    </IconButton>
                                    <IconButton 
                                      size="small" 
                                      onClick={() => handleEditWidget(widget._id)}
                                      title="Edit widget settings"
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                    <IconButton 
                                      size="small" 
                                      onClick={() => handleRemoveWidget(widget._id)}
                                      title="Remove widget"
                                    >
                                      <CloseIcon fontSize="small" />
                                    </IconButton>
                                  </Box>
                                  {renderWidget(widget, handleRemoveWidget, handleEditWidget, getWidgetBreakpoints(widget.position?.w || 3))}
                                  {/* Resize handles for visual feedback */}
                                  <Box className="resize-handles" />
                                </Box>
                              </Paper>
                            </div>
                          )}
                        </Draggable>
                      );
                    })}
                    {provided.placeholder}
                    </div>
                  );
                }}
              </Droppable>
            </DragDropContext>
          ) : (
            <Grid container spacing={3}>
              {filterWidgetsByPermission(preferences?.widgets)?.map(widget => {
                const gridSize = getWidgetGridSize(widget);
                
                return (
                  <Grid item xs={gridSize.xs} sm={gridSize.sm} md={gridSize.md} key={widget._id.toString()}>
                    <Paper 
                      sx={{ 
                        p: 2, 
                        height: '100%',
                        transition: 'all 0.3s ease',
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: '12px',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                        '&:hover': {
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
                          transform: 'translateY(-2px)'
                        }
                      }}
                    >
                      <Box sx={{ position: 'relative' }}>
                        {renderWidget(widget, handleRemoveWidget, handleEditWidget, getWidgetBreakpoints(widget.position?.w || 3))}
                      </Box>
                    </Paper>
                  </Grid>
                );
              })}
            </Grid>
          )}

          {isEditMode && (
            <Tooltip title="Add Widget">
              <Fab 
                color="primary" 
                aria-label="add widget"
                onClick={handleAddWidgetOpen}
                sx={{ position: 'fixed', bottom: 20, right: 20 }}
              >
                <AddIcon />
              </Fab>
            </Tooltip>
          )}
        </Box>
      )}

      {/* Add Widget Dialog */}
      <Dialog 
        open={addWidgetOpen} 
        onClose={handleAddWidgetClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Widget
          <IconButton
            aria-label="close"
            onClick={handleAddWidgetClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <List>
            {widgetTypes.filter((wt) => canUseWidgetType(wt.type)).map((widgetType) => (
              <ListItemButton 
                key={widgetType.type}
                onClick={() => handleAddWidget(widgetType.type)}
              >
                <ListItemText 
                  primary={widgetType.title} 
                  secondary={widgetType.description} 
                />
              </ListItemButton>
            ))}
          </List>
        </DialogContent>
      </Dialog>

      {/* Widget Settings Dialog */}
      {currentWidget && (
        <Dialog 
          open={settingsOpen} 
          onClose={handleSettingsClose}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Widget Settings
            <IconButton
              aria-label="close"
              onClick={handleSettingsClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Typography variant="subtitle1" gutterBottom>
              Title
            </Typography>
            <TextField 
              fullWidth
              margin="normal"
              value={widgetTitle}
              onChange={(e) => setWidgetTitle(e.target.value)}
              placeholder="Widget Title"
              sx={{ mb: 3 }}
            />

            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle1" gutterBottom>
              Settings
            </Typography>
            <WidgetSettingsForm 
              widgetType={currentWidget.type}
              initialSettings={currentWidget.settings}
              onChange={setWidgetFormSettings}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleSettingsClose}>Cancel</Button>
            <Button 
              onClick={handleSaveSettings}
              variant="contained"
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default DashboardPage;
