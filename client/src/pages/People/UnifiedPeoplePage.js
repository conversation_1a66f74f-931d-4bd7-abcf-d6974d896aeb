import React, { useEffect, useMemo, useState } from 'react';
import { Container, Box, Tabs, Tab, Paper } from '@mui/material';
import { useSearchParams } from 'react-router-dom';

// Existing pages to embed
import PeoplePage from './PeoplePage';
import StaffDirectoryPage from '../StaffDirectory/StaffDirectoryPage';
import PhoneBookPage from '../PhoneBook/PhoneBookPage';

/**
 * Unified People Page
 * Combines People Directory, Staff Directory, and Phone Book into a single page with tabs.
 * - Uses URL query param `peopleTab` to sync the active tab (directory | staff | phone)
 * - Avoids interfering with StaffDirectoryPage internal URL tab sync by passing disableUrlSync
 */
const UnifiedPeoplePage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [tabIndex, setTabIndex] = useState(0);

  const tabKeyToIndex = useMemo(() => ({ directory: 0, staff: 1, phone: 2 }), []);
  const indexToTabKey = useMemo(() => ({ 0: 'directory', 1: 'staff', 2: 'phone' }), []);

  // Initialize tab from URL
  useEffect(() => {
    const key = searchParams.get('peopleTab');
    if (key && key in tabKeyToIndex) {
      setTabIndex(tabKeyToIndex[key]);
    } else if (!key) {
      // Default to directory tab without clobbering unrelated params
      const newParams = new URLSearchParams(searchParams);
      newParams.set('peopleTab', 'directory');
      setSearchParams(newParams, { replace: true });
    }
  }, [searchParams, setSearchParams, tabKeyToIndex]);

  const handleTabChange = (event, newIndex) => {
    setTabIndex(newIndex);
    const newParams = new URLSearchParams(searchParams);
    newParams.set('peopleTab', indexToTabKey[newIndex]);
    setSearchParams(newParams, { replace: true });
  };

  return (
    <Container maxWidth="xl">
      <Paper sx={{ width: '100%', mt: 3 }}>
        <Tabs
          value={tabIndex}
          onChange={handleTabChange}
          aria-label="Unified People Tabs"
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
        >
          <Tab label="Directory" />
          <Tab label="Staff" />
          <Tab label="Phone Book" />
        </Tabs>
      </Paper>

      <Box sx={{ mt: 2 }}>
        {tabIndex === 0 && (
          // People Directory (local + Planning Center)
          <PeoplePage />
        )}

        {tabIndex === 1 && (
          // Staff Directory (URL sync disabled to avoid conflicting with unified tab)
          <StaffDirectoryPage disableUrlSync />
        )}

        {tabIndex === 2 && (
          // Phone Book
          <PhoneBookPage />
        )}
      </Box>
    </Container>
  );
};

export default UnifiedPeoplePage;
