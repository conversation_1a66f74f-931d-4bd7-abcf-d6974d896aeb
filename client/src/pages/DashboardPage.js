import React, { useState, useCallback, useRef } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Button,
  Divider,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  TextField,
  Icon
} from '@mui/material';
import { 
  Add as AddIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Close as CloseIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon,
  ArrowBack as ArrowLeftIcon,
  ArrowForward as ArrowRightIcon,
  AspectRatio as ResizeIcon,
  MoreVert as MoreVertIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import { useDashboard } from '../context/DashboardContext';
import { renderWidget, widgetTypes } from '../components/widgets/WidgetRegistry';
import WidgetSettingsForm from '../components/widgets/WidgetSettingsForm';
import { debounce } from '../utils/performance';

// Calculate widget width based breakpoints for responsive behavior
// Note: Don't mark widgets as xs or sm unless they are truly tiny to preserve icons
const getWidgetBreakpoints = (widgetGridWidth, containerWidth = 1200) => {
  // Calculate the actual pixel width based on grid position
  const actualWidth = (widgetGridWidth / 12) * containerWidth;
  
  // Define breakpoints - be more conservative about marking widgets as xs/sm
  // to preserve icons and functionality
  return {
    xs: false,  // Never mark as xs to preserve icons
    sm: actualWidth < 250,     // Only very small widgets are sm
    md: actualWidth >= 250 && actualWidth < 600,    // Most widgets
    lg: actualWidth >= 600 && actualWidth < 900,    // Large widgets
    xl: actualWidth >= 900     // Extra large widgets
  };
};

const DashboardPage = () => {
  const { user, hasPermission } = useAuth();
  const { 
    preferences, 
    unsavedPreferences,
    loading, 
    error, 
    isEditMode, 
    hasUnsavedChanges,
    toggleEditMode, 
    addWidget, 
    updateWidget, 
    removeWidget, 
    resetDashboard, 
    updateLayout: originalUpdateLayout,
    saveDashboardChanges,
    discardChanges
  } = useDashboard();

  // Track if a resize operation is in progress
  const [isResizing, setIsResizing] = useState(false);

  // Create a debounced function to reset the resizing flag
  const debouncedResetResizing = useCallback(
    debounce(() => {
      setIsResizing(false);
    }, 500),
    []
  );

  // State for add widget dialog
  const [addWidgetOpen, setAddWidgetOpen] = useState(false);

  // State for widget settings dialog
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [settingsWidgetId, setSettingsWidgetId] = useState(null);

  // State for menu
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const menuOpen = Boolean(menuAnchorEl);

  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle opening add widget dialog
  const handleAddWidgetOpen = () => {
    setAddWidgetOpen(true);
  };

  // Handle closing add widget dialog
  const handleAddWidgetClose = () => {
    setAddWidgetOpen(false);
  };

  // Handle adding a new widget
  const handleAddWidget = async (widgetType) => {
    await addWidget(widgetType);
    handleAddWidgetClose();
  };

  // Handle removing a widget
  const handleRemoveWidget = (widgetId) => {
    if (window.confirm('Are you sure you want to remove this widget?')) {
      removeWidget(widgetId);
    }
  };

  // Handle opening settings for a widget
  const handleEditWidget = (widgetId) => {
    setSettingsWidgetId(widgetId);
    setSettingsOpen(true);
  };

  // Handle closing widget settings
  const handleCloseSettings = () => {
    setSettingsOpen(false);
    setSettingsWidgetId(null);
  };

  // Handle saving widget settings
  const handleSaveWidgetSettings = (widgetId, settings) => {
    updateWidget(widgetId, settings);
    handleCloseSettings();
  };

  // Handle widget resize cycling (33% -> 50% -> 66% -> 100% -> 33%)
  const handleWidgetResize = (widgetId) => {
    const widget = unsavedPreferences?.widgets?.find(w => w._id === widgetId);
    if (!widget) return;

    // Set resizing flag to prevent loading indicator
    setIsResizing(true);

    const currentWidth = widget.position?.w || 4;
    let nextWidth, nextHeight = 2;

    // Cycle through sizes: 4 (33%) -> 6 (50%) -> 8 (66%) -> 12 (100%) -> 4
    if (currentWidth <= 4) {
      nextWidth = 6;  // 50%
    } else if (currentWidth <= 6) {
      nextWidth = 8;  // 66%
    } else if (currentWidth <= 8) {
      nextWidth = 12; // 100%
      nextHeight = 3;
    } else {
      nextWidth = 4;  // Back to 33%
    }

    // Update widget size
    updateWidget(widgetId, {
      position: {
        ...widget.position,
        w: nextWidth,
        h: nextHeight
      }
    });

    // Reset resizing flag
    debouncedResetResizing();
  };

  // Handle moving widgets with arrow buttons
  const handleMoveWidget = (widgetId, direction) => {
    if (!isEditMode || !unsavedPreferences?.widgets) return;
    
    // Set resizing flag to prevent loading indicator
    setIsResizing(true);
    
    // Work with the visually displayed ordering (sorted by position)
    const widgets = [...displayedWidgets];
    const currentIndex = widgets.findIndex(w => w._id === widgetId);
    
    if (currentIndex === -1) {
      setIsResizing(false);
      return;
    }
    
    let newIndex = currentIndex;
    const totalWidgets = widgets.length;
    
    switch (direction) {
      case 'up':
        // Move earlier in the list
        newIndex = Math.max(0, currentIndex - 1);
        break;
      case 'down':
        // Move later in the list
        newIndex = Math.min(totalWidgets - 1, currentIndex + 1);
        break;
      case 'left':
        // Move earlier in the list
        newIndex = Math.max(0, currentIndex - 1);
        break;
      case 'right':
        // Move later in the list
        newIndex = Math.min(totalWidgets - 1, currentIndex + 1);
        break;
    }
    
    if (newIndex !== currentIndex) {
      // Remove widget from current position and insert at new position
      const [movedWidget] = widgets.splice(currentIndex, 1);
      widgets.splice(newIndex, 0, movedWidget);
      
      // Create updated layout array and delegate to context updateLayout
      const updatedLayout = widgets.map((widget, index) => ({
        i: widget._id.toString(),
        x: 0,
        y: index,
        w: widget.position?.w || 4,
        h: widget.position?.h || 2
      }));
      
      // Update the layout via context
      originalUpdateLayout(updatedLayout);
    }
    
    // Reset resizing flag
    debouncedResetResizing();
  };

  // Handle saving dashboard changes
  const handleSaveDashboard = async () => {
    if (hasUnsavedChanges) {
      try {
        await saveDashboardChanges();
        // Pass true to toggleEditMode to skip saving again
        toggleEditMode(true);
      } catch (err) {
        console.error('Error saving dashboard:', err);
        // You might want to show an error message to the user here
        return; // Don't exit edit mode if saving fails
      }
    } else {
      // If no unsaved changes, just toggle edit mode
      toggleEditMode();
    }
  };

  // Handle resetting the dashboard
  const handleResetDashboard = () => {
    if (window.confirm('Are you sure you want to reset your dashboard to default?')) {
      resetDashboard();
      handleMenuClose();
    }
  };

  // Determine grid size for each widget - default to 3 per row for compact layout
  const getWidgetGridSize = (widget) => {
    // Default sizes for compact 3-per-row layout
    let xs = 12; // Full width on mobile
    let sm = 6;  // Half width on tablets
    let md = 4;  // One-third width on desktops (3 per row)
    
    // Ensure widget.position exists
    const position = widget.position || { w: 4, h: 2 };
    
    // Size based on widget's position.w property
    if (position.w >= 12) {
      // Full width widgets (100%)
      md = 12;
      sm = 12;
    } else if (position.w >= 8) {
      // Large widgets (66% width)
      md = 8;
      sm = 12;
    } else if (position.w >= 6) {
      // Medium widgets (50% width) - 2 per row
      md = 6;
      sm = 12;
    } else {
      // Standard widgets (33% width) - 3 per row (default)
      md = 4;
      sm = 6;
    }
    
    return { xs, sm, md };
  };

  // Determine CSS Grid column span based on widget width
  const getWidgetGridColumnSpan = (widget) => {
    const w = widget?.position?.w || 4;
    if (w >= 12) return '1 / -1'; // span entire row
    if (w >= 6) return 'span 2';  // medium/large widgets take two columns
    return 'span 1';              // small widgets take one column
  };

  // Permission helper: check if current user can access a given widget type
  const canUseWidgetType = (type) => {
    const def = widgetTypes.find(w => w.type === type);
    const required = def && def.requiredPermission;
    // If no required permission specified, default to allowed
    if (!required) return true;
    // Use AuthContext hasPermission
    return typeof hasPermission === 'function' ? hasPermission(required) : true;
  };

  // Filter widgets in preferences based on permissions
  const filterWidgetsByPermission = (widgets) => {
    if (!Array.isArray(widgets)) return [];
    return widgets.filter((w) => canUseWidgetType(w.type));
  };

  // Determine the list of widgets to display, filtered by permissions
  // and sorted by grid position (row: y, then column: x). This ensures
  // that visual order matches position updates from layout changes.
  const rawWidgets = (isEditMode ? filterWidgetsByPermission(unsavedPreferences?.widgets) : filterWidgetsByPermission(preferences?.widgets));
  const displayedWidgets = [...rawWidgets].sort((a, b) => {
    const ay = a?.position?.y ?? 0;
    const by = b?.position?.y ?? 0;
    if (ay !== by) return ay - by;
    const ax = a?.position?.x ?? 0;
    const bx = b?.position?.x ?? 0;
    return ax - bx;
  });

  return (
    <Container maxWidth="xl">
      <Box sx={{ 
        mb: 4, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        borderRadius: '12px',
        p: 3,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
      }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom sx={{ color: '#2d3748 !important', fontWeight: 700 }}>
            Welcome, {user?.name}
          </Typography>
          <Typography variant="body1" sx={{ color: '#718096 !important', fontSize: '1.1rem' }}>
            Here's your dashboard with quick access to important resources
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Tooltip title={isEditMode ? "Save Layout" : "Customize Dashboard"}>
            <IconButton 
              color="#000000"
              sx={{ color: '#000000 !important' }}
              onClick={isEditMode ? handleSaveDashboard : () => toggleEditMode()}
            >
              {isEditMode ? <SaveIcon /> : <EditIcon />}
            </IconButton>
          </Tooltip>
          {isEditMode && (
            <Tooltip title="Add Widget">
              <IconButton 
                color="#000000"
                sx={{ color: '#000000 !important' }}
                onClick={handleAddWidgetOpen}
              >
                <AddIcon />
              </IconButton>
            </Tooltip>
          )}
          <Tooltip title="Dashboard Options">
            <IconButton
              onClick={handleMenuOpen}
              aria-controls={menuOpen ? 'dashboard-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={menuOpen ? 'true' : undefined}
              color="#000000"
              sx={{ color: '#000000 !important' }}
            >
              <DashboardIcon />
            </IconButton>
          </Tooltip>
          <Menu
            id="dashboard-menu"
            anchorEl={menuAnchorEl}
            open={menuOpen}
            onClose={handleMenuClose}
            MenuListProps={{
              'aria-labelledby': 'dashboard-menu-button',
            }}
          >
            <MenuItem onClick={handleResetDashboard}>
              <ListItemIcon>
                <RefreshIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Reset Dashboard</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {loading && !isResizing ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : displayedWidgets.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Your dashboard is empty
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Add widgets to customize your dashboard
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleAddWidgetOpen}
          >
            Add Widget
          </Button>
        </Paper>
      ) : (
        <Box sx={{ position: 'relative', pb: 8 }}>
          <Box className="dashboard-grid" sx={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))',
            gap: 2,
            alignItems: 'stretch',
            gridAutoFlow: 'row dense'
          }}>
            {displayedWidgets.map((widget, index) => {
              const isFirstItem = index === 0;
              const isLastItem = index === displayedWidgets.length - 1;
              
              return (
                <Box key={widget._id.toString()} sx={{ gridColumn: { xs: 'span 1', md: getWidgetGridColumnSpan(widget) } }}>
                  <Paper 
                    sx={{ 
                      p: 1.5, 
                      height: '100%',
                      position: 'relative',
                      transition: 'all 0.3s ease',
                      background: 'rgba(255, 255, 255, 0.98)',
                      backdropFilter: 'blur(10px)',
                      borderRadius: '12px',
                      boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                      border: '1px solid rgba(0, 0, 0, 0.06)',
                      '&:hover': {
                        boxShadow: isEditMode ? '0 4px 16px rgba(0, 0, 0, 0.12)' : '0 4px 12px rgba(0, 0, 0, 0.1)',
                        transform: 'translateY(-1px)',
                        '& .widget-controls': {
                          opacity: 1
                        }
                      }
                    }}
                  >
                    <Box sx={{ position: 'relative' }}>
                      {isEditMode && (
                        <Box 
                          className="widget-controls"
                          sx={{ 
                            position: 'absolute', 
                            top: -8, 
                            right: -8, 
                            zIndex: 10,
                            display: 'flex',
                            gap: 0.5,
                            opacity: 0,
                            transition: 'opacity 0.2s ease',
                            background: 'rgba(255, 255, 255, 0.95)',
                            borderRadius: '8px',
                            padding: '4px',
                            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)'
                          }}
                        >
                          {/* Arrow controls */}
                          <Tooltip title="Move up">
                            <IconButton 
                              size="small" 
                              onClick={() => handleMoveWidget(widget._id, 'up')}
                              disabled={isFirstItem}
                              sx={{ 
                                width: 28, 
                                height: 28,
                                '&:hover': {
                                  backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                  color: '#2563eb'
                                }
                              }}
                            >
                              <ArrowUpIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Move down">
                            <IconButton 
                              size="small" 
                              onClick={() => handleMoveWidget(widget._id, 'down')}
                              disabled={isLastItem}
                              sx={{ 
                                width: 28, 
                                height: 28,
                                '&:hover': {
                                  backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                  color: '#2563eb'
                                }
                              }}
                            >
                              <ArrowDownIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Move left">
                            <IconButton 
                              size="small" 
                              onClick={() => handleMoveWidget(widget._id, 'left')}
                              disabled={isFirstItem}
                              sx={{ 
                                width: 28, 
                                height: 28,
                                '&:hover': {
                                  backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                  color: '#2563eb'
                                }
                              }}
                            >
                              <ArrowLeftIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Move right">
                            <IconButton 
                              size="small" 
                              onClick={() => handleMoveWidget(widget._id, 'right')}
                              disabled={isLastInRow}
                              sx={{ 
                                width: 28, 
                                height: 28,
                                '&:hover': {
                                  backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                  color: '#2563eb'
                                }
                              }}
                            >
                              <ArrowRightIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          
                          {/* Divider */}
                          <Divider orientation="vertical" flexItem sx={{ mx: 0.5 }} />
                          
                          {/* Other controls */}
                          <Tooltip title="Resize widget">
                            <IconButton 
                              size="small" 
                              onClick={() => handleWidgetResize(widget._id)}
                              sx={{ 
                                width: 28, 
                                height: 28,
                                '&:hover': {
                                  backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                  color: '#2563eb'
                                }
                              }}
                            >
                              <ResizeIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit settings">
                            <IconButton 
                              size="small" 
                              onClick={() => handleEditWidget(widget._id)}
                              sx={{ 
                                width: 28, 
                                height: 28,
                                '&:hover': {
                                  backgroundColor: 'rgba(37, 99, 235, 0.1)',
                                  color: '#2563eb'
                                }
                              }}
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Remove widget">
                            <IconButton 
                              size="small" 
                              onClick={() => handleRemoveWidget(widget._id)}
                              sx={{ 
                                width: 28, 
                                height: 28,
                                '&:hover': {
                                  backgroundColor: 'rgba(220, 38, 38, 0.1)',
                                  color: '#dc2626'
                                }
                              }}
                            >
                              <CloseIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      )}
                      {renderWidget(widget, handleRemoveWidget, handleEditWidget, getWidgetBreakpoints(widget.position?.w || 4))}
                    </Box>
                  </Paper>
                </Box>
              );
            })}
          </Box>

        </Box>
      )}

      {/* Add Widget Dialog */}
      <Dialog 
        open={addWidgetOpen} 
        onClose={handleAddWidgetClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Widget
          <IconButton
            aria-label="close"
            onClick={handleAddWidgetClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <List>
            {widgetTypes
              .filter(type => canUseWidgetType(type.type))
              .map(type => (
                <ListItemButton 
                  key={type.type} 
                  onClick={() => handleAddWidget(type.type)}
                >
                  <ListItemIcon><Icon>{type.icon}</Icon></ListItemIcon>
                  <ListItemText 
                    primary={type.name || type.title} 
                    secondary={type.description}
                  />
                </ListItemButton>
              ))}
          </List>
        </DialogContent>
      </Dialog>

      {/* Widget Settings Dialog */}
      <Dialog 
        open={settingsOpen} 
        onClose={handleCloseSettings}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Widget Settings
          <IconButton
            aria-label="close"
            onClick={handleCloseSettings}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          {settingsWidgetId && (
            <WidgetSettingsForm
              widgetId={settingsWidgetId}
              widget={displayedWidgets.find(w => w._id === settingsWidgetId)}
              onSave={handleSaveWidgetSettings}
              onCancel={handleCloseSettings}
            />
          )}
        </DialogContent>
      </Dialog>
    </Container>
  );
};

export default DashboardPage;