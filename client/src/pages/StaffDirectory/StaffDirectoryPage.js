import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typo<PERSON>, 
  Grid, 
  Card, 
  CardContent, 
  Avatar, 
  TextField, 
  InputAdornment,
  Chip,
  Button,
  IconButton,
  Divider,
  CircularProgress,
  Paper,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  Tooltip
} from '@mui/material';
import { 
  Search as SearchIcon,
  Person as PersonIcon,
  Group as GroupIcon,
  LocalOffer as TagIcon,
  Refresh as RefreshIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  SyncAlt as SyncIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import axios from 'axios';
import { useAuth } from '../../context/AuthContext';
import googleAdminService from '../../services/googleAdminService';
import GroupManagementDialog from './GroupManagementDialog';
import TeamManagementDialog from './TeamManagementDialog';
import websocketService from '../../services/websocketService';

const ONLINE_THRESHOLD_MS = 30 * 60 * 1000; // 30 minutes
const StaffDirectoryPage = ({ disableUrlSync = false }) => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  
  const [users, setUsers] = useState([]);
  const [teams, setTeams] = useState([]);
  const [groups, setGroups] = useState([]);
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [tabValue, setTabValue] = useState(0);
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [departments, setDepartments] = useState([]);
  const [typeFilter, setTypeFilter] = useState('');
  const userTypes = ['student', 'staff', 'contractor', 'resident', 'intern', 'other'];
  
  // Dialog control state
  const [teamDialogOpen, setTeamDialogOpen] = useState(false);
  const [groupDialogOpen, setGroupDialogOpen] = useState(false);
  const [selectedTeamId, setSelectedTeamId] = useState(null);
  const [selectedGroupId, setSelectedGroupId] = useState(null);
  
  // Pagination state for users
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(25);
  const [totalUsers, setTotalUsers] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  
  // Pagination state for teams
  const [teamsPage, setTeamsPage] = useState(1);
  const [teamsLimit] = useState(25);
  const [totalTeams, setTotalTeams] = useState(0);
  const [totalTeamsPages, setTotalTeamsPages] = useState(1);
  
  // Pagination state for groups
  const [groupsPage, setGroupsPage] = useState(1);
  const [groupsLimit] = useState(25);
  const [totalGroups, setTotalGroups] = useState(0);
  const [totalGroupsPages, setTotalGroupsPages] = useState(1);
  
  // Read tab from URL on component mount
  useEffect(() => {
    if (disableUrlSync) return;
    const tabParam = searchParams.get('tab');
    if (tabParam) {
      // Set the active tab based on the URL parameter
      if (tabParam === 'people') {
        setTabValue(0);
      } else if (tabParam === 'teams') {
        setTabValue(1);
      } else if (tabParam === 'groups') {
        setTabValue(2);
      }
    } else {
      // If no tab parameter is present, set the default tab and update URL
      navigate({ pathname: location.pathname, search: '?tab=people' }, { replace: true });
    }
  }, [searchParams, navigate, location.pathname, disableUrlSync]);

  // Subscribe to presence updates
  useEffect(() => {
    const handler = (payload) => {
      setUsers(prev => prev.map(u => u._id === payload.userId ? { ...u, presence: payload.presence } : u));
    };
    websocketService.addEventListener('presence', handler);
    return () => {
      websocketService.removeEventListener('presence', handler);
    };
  }, []);

  // Fetch users, teams, groups, and tags
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Always use pagination, but pass filter parameters to the API
        // Build query parameters for users API
        const userParams = new URLSearchParams({
          page: page,
          limit: limit
        });
        
        // Add filter parameters if they exist
        if (searchTerm) userParams.append('search', searchTerm);
        if (departmentFilter) userParams.append('department', departmentFilter);
        if (typeFilter) userParams.append('type', typeFilter);
        
        // Fetch users with pagination and filters
        const [usersRes, teamsRes, groupsRes, tagsRes] = await Promise.all([
          axios.get(`/api/staff-directory/users?${userParams.toString()}`),
          axios.get(`/api/staff-directory/teams?page=${teamsPage}&limit=${teamsLimit}`),
          axios.get(`/api/staff-directory/groups?page=${groupsPage}&limit=${groupsLimit}`),
          axios.get('/api/staff-directory/tags')
        ]);

        // Handle users response
        const { users: fetchedUsers, pagination } = usersRes.data;
        setUsers(fetchedUsers);
        
        // Always update pagination state
        setTotalUsers(pagination.total);
        setTotalPages(pagination.pages);
        
        // Handle teams response with pagination
        const { teams: fetchedTeams, pagination: teamsPagination } = teamsRes.data;
        setTeams(fetchedTeams);
        setTotalTeams(teamsPagination.total);
        setTotalTeamsPages(teamsPagination.pages);
        
        // Handle groups response with pagination
        const { groups: fetchedGroups, pagination: groupsPagination } = groupsRes.data;
        console.log('Groups data from API:', fetchedGroups);
        setGroups(fetchedGroups);
        setTotalGroups(groupsPagination.total);
        setTotalGroupsPages(groupsPagination.pages);
        setTags(tagsRes.data);

        // Extract unique departments for teams filtering
        // Always use all fetched users for teams list
        const depts = [...new Set(fetchedUsers.map(user => user.department).filter(Boolean))];
        setDepartments(depts);

        setError(null);
      } catch (err) {
        console.error('Error fetching staff directory data:', err);
        setError('Failed to load staff directory data. Please try again later.');
        
        // Reset pagination state on API failure to avoid showing stale data
        setTotalUsers(0);
        setTotalPages(1);
        setTotalTeams(0);
        setTotalTeamsPages(1);
        setTotalGroups(0);
        setTotalGroupsPages(1);
        
        // Reset data
        setUsers([]);
        setTeams([]);
        setGroups([]);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [page, limit, teamsPage, teamsLimit, groupsPage, groupsLimit, searchTerm, departmentFilter, typeFilter]); // Re-fetch when these values change

  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
    
    // Reset to page 1 when changing tabs
    if (newValue === 0) {
      setPage(1);
      // Update URL to reflect People tab
      if (!disableUrlSync) {
        navigate({ pathname: location.pathname, search: '?tab=people' }, { replace: true });
      }
    } else if (newValue === 1) {
      setTeamsPage(1);
      // Update URL to reflect Teams tab
      if (!disableUrlSync) {
        navigate({ pathname: location.pathname, search: '?tab=teams' }, { replace: true });
      }
    } else if (newValue === 2) {
      setGroupsPage(1);
      // Update URL to reflect Groups tab
      if (!disableUrlSync) {
        navigate({ pathname: location.pathname, search: '?tab=groups' }, { replace: true });
      }
    }
  };
  
  // Handle page change for users
  const handlePageChange = (event, newPage) => {
    setPage(newPage);
    // Scroll to top when page changes
    window.scrollTo(0, 0);
  };
  
  // Handle page change for teams
  const handleTeamsPageChange = (event, newPage) => {
    setTeamsPage(newPage);
    // Scroll to top when page changes
    window.scrollTo(0, 0);
  };
  
  // Handle page change for groups
  const handleGroupsPageChange = (event, newPage) => {
    setGroupsPage(newPage);
    // Scroll to top when page changes
    window.scrollTo(0, 0);
  };

  // Filter users based on search term and team filter
  const filteredUsers = users.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.email && user.email.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.jobTitle && user.jobTitle.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.department && user.department.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (user.skills && Array.isArray(user.skills) && user.skills.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()))) ||
      (user.tags && Array.isArray(user.tags) && user.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())));

    const matchesDepartment = departmentFilter === '' || user.department === departmentFilter;
    const matchesType = typeFilter === '' || user.type === typeFilter;

    return matchesSearch && matchesDepartment && matchesType;
  });

  // Filter teams based on search term
  const filteredTeams = teams.filter(team => 
    searchTerm === '' || 
    team.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (team.description && team.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (team.tags && Array.isArray(team.tags) && team.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
  );

  // Filter groups based on search term
  const filteredGroups = groups.filter(group => 
    searchTerm === '' || 
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
    (group.tags && Array.isArray(group.tags) && group.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase())))
  );

  // Render user card
  const renderUserCard = (user) => (
    <Grid item xs={12} sm={6} md={4} lg={3} key={user._id}>
      <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar 
              src={user.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(user.avatar)}` : undefined} 
              alt={user.name}
              sx={{ width: 64, height: 64, mr: 2 }}
            />
            <Box>
              <Typography variant="h6" component={Link} to={`/staff-directory/users/${user._id}`} sx={{ textDecoration: 'none', color: 'inherit' }}>
                {user.name}
              </Typography>
              {(() => {
                const last = user && user.presence && user.presence.lastActiveAt ? new Date(user.presence.lastActiveAt).getTime() : 0;
                const active = user && user.isActive !== false;
                const online = active && last && (Date.now() - last) < ONLINE_THRESHOLD_MS;
                const status = user && user.presence && user.presence.status;
                const message = user && user.presence && user.presence.statusMessage;
                const label = online ? ((status && status !== 'available') ? (message || status) : 'Online') : 'Offline';
                return (
                  <Typography variant="body2" color={online ? 'success.main' : 'text.secondary'}>
                    {label}
                  </Typography>
                );
              })()}
              {user.jobTitle && (
                <Typography variant="body2" color="text.secondary">
                  {user.jobTitle}
                </Typography>
              )}
              {user.department && (
                <Typography variant="body2" color="text.secondary">
                  {user.department}
                </Typography>
              )}
              {user.type && (
                <Typography variant="body2" color="text.secondary">
                  Role: {user.type.charAt(0).toUpperCase() + user.type.slice(1)}
                </Typography>
              )}
            </Box>
          </Box>

          <Divider sx={{ my: 1 }} />

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
            {user.email && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <EmailIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" component="a" href={`mailto:${user.email}`} sx={{ textDecoration: 'none', color: 'primary.main' }}>
                  {user.email}
                </Typography>
              </Box>
            )}

            {user.phoneNumber && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <PhoneIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2" component="a" href={`tel:${user.phoneNumber}`} sx={{ textDecoration: 'none', color: 'primary.main' }}>
                  {user.phoneNumber}
                </Typography>
              </Box>
            )}

            {user.location && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocationIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                <Typography variant="body2">
                  {user.location}
                </Typography>
              </Box>
            )}
          </Box>

          {(user.skills && Array.isArray(user.skills) && user.skills.length > 0) && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Skills
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {user.skills.map((skill, index) => (
                  <Chip 
                    key={index} 
                    label={skill} 
                    size="small" 
                    variant="outlined"
                  />
                ))}
              </Box>
            </Box>
          )}

          {(user.tags && Array.isArray(user.tags) && user.tags.length > 0) && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Tags
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {user.tags.map((tag, index) => (
                  <Chip 
                    key={index} 
                    label={tag} 
                    size="small"
                    color="primary"
                  />
                ))}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    </Grid>
  );

  // Render team card
  const renderTeamCard = (team) => (
    <Grid item xs={12} sm={6} md={4} key={team._id}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Typography variant="h6" component={Link} to={`/staff-directory/teams/${team._id}`} sx={{ textDecoration: 'none', color: 'inherit' }}>
              {team.name}
            </Typography>
            {user && user.roles.includes('admin') && (
              <IconButton 
                size="small" 
                color="primary"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  setSelectedTeamId(team._id);
                  setTeamDialogOpen(true);
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            )}
          </Box>

          {team.description && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {team.description}
            </Typography>
          )}

          <Divider sx={{ my: 2 }} />

          {team.leader && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Team Lead
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar 
                  src={team.leader.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(team.leader.avatar)}` : undefined} 
                  alt={team.leader.name}
                  sx={{ width: 24, height: 24, mr: 1 }}
                />
                <Typography variant="body2">
                  {team.leader.name}
                </Typography>
              </Box>
            </Box>
          )}

          <Typography variant="subtitle2" gutterBottom>
            Members ({team.members && Array.isArray(team.members) ? team.members.length : 0})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {team.members && Array.isArray(team.members) && team.members.slice(0, 5).map((member) => (
              <Chip
                key={member._id}
                avatar={<Avatar alt={member.name} src={member.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(member.avatar)}` : undefined} />}
                label={member.name}
                size="small"
                variant="outlined"
                component={Link}
                to={`/staff-directory/users/${member._id}`}
                clickable
              />
            ))}
            {team.members && Array.isArray(team.members) && team.members.length > 5 && (
              <Chip
                label={`+${team.members.length - 5} more`}
                size="small"
                variant="outlined"
                component={Link}
                to={`/staff-directory/teams/${team._id}`}
                clickable
              />
            )}
          </Box>

          {(team.tags && Array.isArray(team.tags) && team.tags.length > 0) && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Tags
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {team.tags.map((tag, index) => (
                  <Chip 
                    key={index} 
                    label={tag} 
                    size="small"
                    color="primary"
                  />
                ))}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    </Grid>
  );

  // Render group card
  const renderGroupCard = (group) => (
    <Grid item xs={12} sm={6} md={4} key={group._id}>
      <Card sx={{ height: '100%' }}>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="h6" component={Link} to={`/staff-directory/groups/${group._id}`} sx={{ textDecoration: 'none', color: 'inherit', mr: 1 }}>
                {group.name}
              </Typography>
              {!group.isPublic && (
                <Chip label="Private" size="small" />
              )}
            </Box>
            <IconButton 
              size="small" 
              color="primary"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setSelectedGroupId(group._id);
                setGroupDialogOpen(true);
              }}
            >
              <EditIcon fontSize="small" />
            </IconButton>
          </Box>

          {group.description && (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              {group.description}
            </Typography>
          )}

          <Divider sx={{ my: 2 }} />

          {group.owner && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Owner
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar 
                  src={group.owner.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(group.owner.avatar)}` : undefined} 
                  alt={group.owner.name}
                  sx={{ width: 24, height: 24, mr: 1 }}
                />
                <Typography variant="body2">
                  {group.owner.name}
                </Typography>
              </Box>
            </Box>
          )}

          <Typography variant="subtitle2" gutterBottom>
            Members ({group.members && Array.isArray(group.members) ? group.members.length : 0})
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {group.members && Array.isArray(group.members) && group.members.slice(0, 5).map((member) => {
              // Check if member is a valid object with required properties
              if (!member || typeof member !== 'object') {
                console.error('Invalid member object:', member);
                return null;
              }
              
              return (
                <Chip
                  key={member._id}
                  avatar={<Avatar alt={member.name} src={member.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(member.avatar)}` : undefined} />}
                  label={member.name}
                  size="small"
                  variant="outlined"
                  component={Link}
                  to={`/staff-directory/users/${member._id}`}
                  clickable
                />
              );
            })}
            {group.members && Array.isArray(group.members) && group.members.length > 5 && (
              <Chip
                label={`+${group.members.length - 5} more`}
                size="small"
                variant="outlined"
                component={Link}
                to={`/staff-directory/groups/${group._id}`}
                clickable
              />
            )}
          </Box>

          {(group.tags && Array.isArray(group.tags) && group.tags.length > 0) && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                Tags
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                {group.tags.map((tag, index) => (
                  <Chip 
                    key={index} 
                    label={tag} 
                    size="small"
                    color="primary"
                  />
                ))}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    </Grid>
  );

  // Handle sync profile with Google
  const handleSyncProfile = async () => {
    try {
      setLoading(true);
      await axios.post('/api/staff-directory/google/sync-profile');
      // Refresh users after sync
      const usersRes = await axios.get('/api/staff-directory/users');
      setUsers(usersRes.data);
      setError(null);
    } catch (err) {
      console.error('Error syncing profile with Google:', err);
      setError('Failed to sync profile with Google. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  // Handle sync all users from Google Workspace
  const handleSyncAllUsers = async () => {
    try {
      setLoading(true);
      const result = await googleAdminService.syncAllUsers();
      // Refresh users after sync
      const usersRes = await axios.get('/api/staff-directory/users');
      setUsers(usersRes.data);
      setError(null);
      console.log('Sync completed:', result);
    } catch (err) {
      console.error('Error syncing all users from Google Workspace:', err);
      setError('Failed to sync all users from Google Workspace. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Staff Directory
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={handleSyncProfile}
            disabled={loading}
          >
            Sync with Google
          </Button>
          {user && user.roles.includes('admin') && (
            <Button
              variant="outlined"
              color="secondary"
              startIcon={<SyncIcon />}
              onClick={handleSyncAllUsers}
              disabled={loading}
            >
              Sync All Users
            </Button>
          )}
        </Box>
      </Box>

      <Paper sx={{ mb: 3 }}>
        <Box sx={{ p: 2 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search by name, email, job title, skills, or tags"
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  // Reset all tabs to first page when search changes
                  setPage(1);
                  setTeamsPage(1);
                  setGroupsPage(1);
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel id="department-filter-label">Filter by Team</InputLabel>
                <Select
                  labelId="department-filter-label"
                  id="department-filter"
                  value={departmentFilter}
                  label="Filter by Team"
                  onChange={(e) => {
                    setDepartmentFilter(e.target.value);
                    // Only reset People tab to first page when team filter changes
                    // since team filter only applies to People tab
                    setPage(1);
                  }}
                >
                  <MenuItem value="">All Teams</MenuItem>
                  {departments.map((dept) => (
                    <MenuItem key={dept} value={dept}>{dept}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel id="type-filter-label">Filter by Role</InputLabel>
                <Select
                  labelId="type-filter-label"
                  id="type-filter"
                  value={typeFilter}
                  label="Filter by Role"
                  onChange={(e) => {
                    setTypeFilter(e.target.value);
                    setPage(1); // Reset to first page when type filter changes
                  }}
                >
                  <MenuItem value="">All Roles</MenuItem>
                  {userTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Box>

        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="fullWidth"
        >
          <Tab icon={<PersonIcon />} label="People" />
          <Tab icon={<GroupIcon />} label="Teams" />
          <Tab icon={<BusinessIcon />} label="Groups" />
        </Tabs>
      </Paper>

      {error && (
        <Box sx={{ mb: 3 }}>
          <Typography color="error">{error}</Typography>
        </Box>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Box sx={{ mb: 4 }}>
          {tabValue === 0 && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  People ({filteredUsers.length})
                </Typography>
                {user && user.roles.includes('admin') && (
                  <Button 
                    variant="contained" 
                    component={Link} 
                    to="/admin/users"
                  >
                    Manage Users
                  </Button>
                )}
              </Box>
              <Grid container spacing={3}>
                {filteredUsers.length > 0 ? (
                  filteredUsers.map(renderUserCard)
                ) : (
                  <Grid item xs={12}>
                    <Typography align="center">No users found matching your search criteria.</Typography>
                  </Grid>
                )}
              </Grid>
              
              {/* Pagination - only show if we have results and multiple pages */}
              {filteredUsers.length > 0 && totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination 
                    count={totalPages} 
                    page={page} 
                    onChange={handlePageChange} 
                    color="primary" 
                    size="large"
                    showFirstButton 
                    showLastButton
                  />
                </Box>
              )}
            </>
          )}

          {tabValue === 1 && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Teams ({filteredTeams.length})
                </Typography>
                {user && user.roles.includes('admin') && (
                  <Button 
                    variant="contained" 
                    onClick={() => {
                      setSelectedTeamId(null);
                      setTeamDialogOpen(true);
                    }}
                  >
                    Create Team
                  </Button>
                )}
              </Box>
              <Grid container spacing={3}>
                {filteredTeams.length > 0 ? (
                  filteredTeams.map(renderTeamCard)
                ) : (
                  <Grid item xs={12}>
                    <Typography align="center">No teams found matching your search criteria.</Typography>
                  </Grid>
                )}
              </Grid>
              
              {/* Teams Pagination - only show if we have results and multiple pages */}
              {filteredTeams.length > 0 && totalTeamsPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination 
                    count={totalTeamsPages} 
                    page={teamsPage} 
                    onChange={handleTeamsPageChange} 
                    color="primary" 
                    size="large"
                    showFirstButton 
                    showLastButton
                  />
                </Box>
              )}
            </>
          )}

          {tabValue === 2 && (
            <>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Groups ({filteredGroups.length})
                </Typography>
                <Button 
                  variant="contained" 
                  onClick={() => {
                    setSelectedGroupId(null);
                    setGroupDialogOpen(true);
                  }}
                >
                  Create Group
                </Button>
              </Box>
              {/* Log filtered groups data for debugging */}
              {console.log('Filtered groups data:', filteredGroups)}
              <Grid container spacing={3}>
                {filteredGroups.length > 0 ? (
                  filteredGroups.map((group, index) => {
                    console.log(`Rendering group ${index}:`, group);
                    return renderGroupCard(group);
                  })
                ) : (
                  <Grid item xs={12}>
                    <Typography align="center">No groups found matching your search criteria.</Typography>
                  </Grid>
                )}
              </Grid>
              
              {/* Groups Pagination - only show if we have results and multiple pages */}
              {filteredGroups.length > 0 && totalGroupsPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination 
                    count={totalGroupsPages} 
                    page={groupsPage} 
                    onChange={handleGroupsPageChange} 
                    color="primary" 
                    size="large"
                    showFirstButton 
                    showLastButton
                  />
                </Box>
              )}
            </>
          )}
        </Box>
      )}
      {/* Team Management Dialog */}
      <TeamManagementDialog
        open={teamDialogOpen}
        onClose={() => setTeamDialogOpen(false)}
        teamId={selectedTeamId}
        onSave={(updatedTeam) => {
          // Refresh teams data after creating or updating a team
          const fetchTeams = async () => {
            try {
              const teamsRes = await axios.get(`/api/staff-directory/teams?page=${teamsPage}&limit=${teamsLimit}`);
              const { teams: fetchedTeams, pagination: teamsPagination } = teamsRes.data;
              setTeams(fetchedTeams);
              setTotalTeams(teamsPagination.total);
              setTotalTeamsPages(teamsPagination.pages);
            } catch (err) {
              console.error('Error fetching teams after update:', err);
            }
          };
          fetchTeams();
        }}
      />

      {/* Group Management Dialog */}
      <GroupManagementDialog
        open={groupDialogOpen}
        onClose={() => setGroupDialogOpen(false)}
        groupId={selectedGroupId}
        onSave={(updatedGroup) => {
          // Refresh groups data after creating or updating a group
          const fetchGroups = async () => {
            try {
              const groupsRes = await axios.get(`/api/staff-directory/groups?page=${groupsPage}&limit=${groupsLimit}`);
              const { groups: fetchedGroups, pagination: groupsPagination } = groupsRes.data;
              setGroups(fetchedGroups);
              setTotalGroups(groupsPagination.total);
              setTotalGroupsPages(groupsPagination.pages);
            } catch (err) {
              console.error('Error fetching groups after update:', err);
            }
          };
          fetchGroups();
        }}
      />
    </Box>
  );
};

export default StaffDirectoryPage;
