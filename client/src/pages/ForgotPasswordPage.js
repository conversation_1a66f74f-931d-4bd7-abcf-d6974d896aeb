import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Paper,
  Alert,
  TextField,
  CircularProgress
} from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import axios from 'axios';

const ForgotPasswordPage = () => {
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [successMsg, setSuccessMsg] = useState('');

  const handleSubmit = async (e) => {
    e.preventDefault();
    setError('');

    const trimmed = email.trim();
    if (!trimmed) {
      setError('Please enter your email address.');
      return;
    }

    setSubmitting(true);
    try {
      const res = await axios.post('/api/auth/forgot-password', { email: trimmed });
      const msg = res?.data?.msg || 'If an account with that email exists, a password reset link has been sent.';
      setSuccessMsg(msg);
    } catch (err) {
      // Do not reveal specifics; show generic message
      setSuccessMsg('If an account with that email exists, a password reset link has been sent.');
      // Optional logging
      // console.error('Forgot password error:', err);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%)',
      display: 'flex',
      alignItems: 'center',
      py: 4
    }}>
      <Container maxWidth="sm">
        <Box sx={{
          textAlign: 'center',
          mb: 4,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: '12px',
          p: 3
        }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'white', fontWeight: 700 }}>
            Forgot Password
          </Typography>
          <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.05rem' }}>
            Enter your CSF email address to receive a password reset link.
          </Typography>
        </Box>

        <Paper
          elevation={0}
          sx={{
            p: 4,
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
          }}
        >
          {error && (
            <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
              {error}
            </Alert>
          )}

          {successMsg ? (
            <>
              <Alert severity="success" sx={{ width: '100%', mb: 2 }}>
                {successMsg}
              </Alert>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                If you don’t see the email within a few minutes, please check your spam folder.
              </Typography>
              <Button
                variant="text"
                startIcon={<ArrowBackIcon />}
                sx={{ mt: 3 }}
                onClick={() => navigate('/login')}
              >
                Back to Login
              </Button>
            </>
          ) : (
            <Box component="form" onSubmit={handleSubmit} sx={{ width: '100%', mt: 1 }}>
              <TextField
                margin="normal"
                required
                fullWidth
                id="email"
                label="Email Address"
                name="email"
                autoComplete="email"
                autoFocus
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={submitting}
              />

              <Button
                type="submit"
                fullWidth
                variant="contained"
                color="primary"
                size="large"
                startIcon={<EmailIcon />}
                disabled={submitting}
                sx={{ mt: 2, py: 1.5 }}
              >
                {submitting ? <CircularProgress size={24} color="inherit" /> : 'Send Reset Link'}
              </Button>

              <Button
                variant="text"
                startIcon={<ArrowBackIcon />}
                sx={{ mt: 2 }}
                onClick={() => navigate('/login')}
              >
                Back to Login
              </Button>
            </Box>
          )}
        </Paper>
      </Container>
    </Box>
  );
};

export default ForgotPasswordPage;
