import React, { useEffect, useState } from 'react';
import { useNavigate, Link as RouterLink } from 'react-router-dom';
import { 
  Box, 
  Button, 
  Container, 
  Typography, 
  Paper, 
  Divider,
  Alert,
  TextField,
  CircularProgress,
  Tabs,
  Tab,
  Link
} from '@mui/material';
import { Google as GoogleIcon, Email as EmailIcon, Login as LoginIcon } from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';

const LoginPage = () => {
  const { user, loading, error, loginWithGoogle, loginWithCredentials } = useAuth();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState(0);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState('');
  const [localLoading, setLocalLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  // Redirect if already logged in
  useEffect(() => {
    if (user && !loading) {
      navigate('/dashboard');
    }
  }, [user, loading, navigate]);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
    // Clear form errors when switching tabs
    setFormError('');
  };

  const validateForm = () => {
    if (!email) {
      setFormError('Email is required');
      return false;
    }
    if (!password) {
      setFormError('Password is required');
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setLocalLoading(true);
    setFormError('');
    
    try {
      const result = await loginWithCredentials(email, password);
      
      if (result.success) {
        // Login successful, will redirect to dashboard via the useEffect
      } else if (result.requires2FA) {
        // Handle 2FA verification (would need a separate component/page)
        navigate(`/verify-2fa/${result.userId}`);
      } else if (result.requiresSetup2FA) {
        // Handle 2FA setup (would need a separate component/page)
        navigate(`/setup-2fa/${result.userId}`);
      } else if (result.error) {
        setFormError(result.error);
      }
    } catch (err) {
      setFormError('An unexpected error occurred. Please try again.');
      console.error('Login error:', err);
    } finally {
      setLocalLoading(false);
    }
  };

  return (
    <Box sx={{ 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1e3a8a 0%, #1e40af 50%, #3b82f6 100%)',
      display: 'flex',
      alignItems: 'center',
      py: 4
    }}>
      <Container maxWidth="sm">
        <Box sx={{ 
          textAlign: 'center',
          mb: 4,
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(10px)',
          borderRadius: '12px',
          p: 3
        }}>
          <Typography variant="h4" component="h1" gutterBottom sx={{ color: 'white', fontWeight: 700 }}>
            Welcome to CSF Staff Portal
          </Typography>
          <Typography variant="body1" sx={{ color: 'rgba(255, 255, 255, 0.8)', fontSize: '1.1rem' }}>
            Please sign in to continue
          </Typography>
        </Box>

        <Paper 
          elevation={0} 
          sx={{ 
            p: 4, 
            display: 'flex', 
            flexDirection: 'column', 
            alignItems: 'center',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
          }}
        >
        {error && (
          <Alert severity="error" sx={{ width: '100%', mb: 3 }}>
            {error}
          </Alert>
        )}

        <Typography variant="h5" component="h2" gutterBottom sx={{ color: '#2d3748', fontWeight: 600 }}>
          Sign In
        </Typography>
        
        <Box sx={{ width: '100%', mb: 3 }}>
          <Tabs 
            value={activeTab} 
            onChange={handleTabChange} 
            variant="fullWidth" 
            indicatorColor="primary"
            textColor="primary"
            aria-label="login method tabs"
          >
            <Tab icon={<GoogleIcon />} label="Google" />
            <Tab icon={<EmailIcon />} label="Email" />
          </Tabs>
        </Box>
        
        {activeTab === 0 ? (
          // Google login tab
          <Box sx={{ width: '100%', mt: 2 }}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              size="large"
              startIcon={<GoogleIcon />}
              onClick={loginWithGoogle}
              disabled={loading}
              sx={{ py: 1.5 }}
            >
              Sign in with Google
            </Button>
          </Box>
        ) : (
          // Email login tab
          <Box 
            component="form" 
            onSubmit={handleSubmit} 
            sx={{ width: '100%', mt: 2 }}
          >
            {formError && (
              <Alert severity="error" sx={{ width: '100%', mb: 2 }}>
                {formError}
              </Alert>
            )}
            
            <TextField
              margin="normal"
              required
              fullWidth
              id="email"
              label="Email Address"
              name="email"
              autoComplete="email"
              autoFocus
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={localLoading}
            />
            
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="Password"
              type="password"
              id="password"
              autoComplete="current-password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              disabled={localLoading}
            />
            
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 1 }}>
              <Typography 
                variant="body2" 
                color="primary" 
                sx={{ 
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' }
                }}
                onClick={() => navigate('/forgot-password')}
              >
                Forgot Password?
              </Typography>
            </Box>
            
            <Button
              type="submit"
              fullWidth
              variant="contained"
              color="primary"
              size="large"
              startIcon={<LoginIcon />}
              disabled={localLoading}
              sx={{ mt: 3, mb: 2, py: 1.5 }}
            >
              {localLoading ? (
                <CircularProgress size={24} color="inherit" />
              ) : (
                'Sign In'
              )}
            </Button>
          </Box>
        )}

        <Box sx={{ width: '100%', mt: 3 }}>
          <Divider>
            <Typography variant="body2" color="text.secondary">
              Important Information
            </Typography>
          </Divider>
          
          <Box sx={{ mt: 2 }}>
            {activeTab === 0 ? (
              <>
                <Typography variant="body2" paragraph>
                  • You must use your CSF email address to sign in
                </Typography>
                <Typography variant="body2" paragraph>
                  • This portal is for authorized CSF staff only
                </Typography>
                <Typography variant="body2" paragraph>
                  • If you have trouble signing in, please contact the IT department
                </Typography>
              </>
            ) : (
              <>
                <Typography variant="body2" paragraph>
                  • This portal is for authorized CSF users only
                </Typography>
                <Typography variant="body2" paragraph>
                  • If you have trouble signing in, please contact the IT department
                </Typography>
              </>
            )}
          </Box>
        </Box>
      </Paper>

        <Box sx={{ mt: 4, textAlign: 'center' }}>
          <Typography variant="body2" sx={{ color: 'rgba(255, 255, 255, 0.8)' }}>
            By signing in, you agree to the{' '}
            <Link component={RouterLink} to="/terms" sx={{ color: 'inherit', textDecoration: 'underline' }}>
              terms of service
            </Link>{' '}and{' '}
            <Link component={RouterLink} to="/privacy" sx={{ color: 'inherit', textDecoration: 'underline' }}>
              privacy policy
            </Link>.
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default LoginPage;