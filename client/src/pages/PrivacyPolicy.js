import React from 'react';
import { Container, Box, Typography, Link, List, ListItem, ListItemText, Divider } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

const PrivacyPolicy = () => {
  const lastUpdated = 'August 27, 2025';

  return (
    <Container maxWidth="md" sx={{ py: 6 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Privacy Policy
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Last updated: {lastUpdated}
        </Typography>
      </Box>

      <Typography variant="body1" paragraph>
        This Privacy Policy describes how the CSF Staff Portal (the "Portal") processes information when used by
        authorized CSF users. The Portal is an internal system intended for organizational use only.
      </Typography>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h5" component="h2" gutterBottom>
        Information We Process
      </Typography>
      <List sx={{ listStyleType: 'disc', pl: 4 }}>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Account and profile data (e.g., name, email, roles, team)." />
        </ListItem>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Authentication details and session identifiers (e.g., HTTP-only session cookies)." />
        </ListItem>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Activity and system logs for security, troubleshooting, and audit purposes." />
        </ListItem>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Data and metadata synchronized from integrated systems (e.g., Google Workspace, UniFi, Synology, Lenel S2/NetBox, Planning Center, RADIUS, Gmail, LG ThinQ, Dreo, Rain Bird)." />
        </ListItem>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Files and records you access via the Portal (subject to your permissions)." />
        </ListItem>
      </List>

      <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 2 }}>
        How We Use Information
      </Typography>
      <List sx={{ listStyleType: 'disc', pl: 4 }}>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Authenticate users and maintain secure sessions (via HTTP-only cookies and Mongo-backed sessions)." />
        </ListItem>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Provide Portal functionality (files, staff directory, tickets, device controls, scheduling)." />
        </ListItem>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Integrate with third-party services you’re authorized to use (e.g., Google Drive/Calendar/Admin, UniFi Network/Protect/Access, Synology, Lenel S2/NetBox, Planning Center, Gmail, etc.)." />
        </ListItem>
        <ListItem sx={{ display: 'list-item', py: 0 }}>
          <ListItemText primary="Improve reliability and security, including monitoring and logging system behavior. In development or when explicitly enabled, debug logs may be emitted over a secured WebSocket to authorized staff." />
        </ListItem>
      </List>

      <Typography variant="h5" component="h2" gutterBottom sx={{ mt: 2 }}>
        Cookies and Similar Technologies
      </Typography>
      <Typography variant="body1" paragraph>
        The Portal uses an HTTP-only, same-site session cookie to keep you signed in. This cookie is stored in your browser
        and cannot be read by client-side scripts. Session data is stored in MongoDB with appropriate TTL and security
        settings. In production, cookies may be marked secure depending on configuration.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        Data Sharing and Transfers
      </Typography>
      <Typography variant="body1" paragraph>
        Data may be exchanged with integrated systems and service providers to deliver functionality. Access is role-based
        and limited to authorized CSF personnel and systems. We do not sell personal information.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        Data Retention
      </Typography>
      <Typography variant="body1" paragraph>
        Session records and operational logs are retained for operational, troubleshooting, and audit needs in line with CSF
        policies. Data synchronized from third-party integrations follows those systems’ retention policies and your
        permissions within them.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        Security
      </Typography>
      <Typography variant="body1" paragraph>
        We implement safeguards including HTTPS, Content Security Policy (CSP), CORS controls, role-based access, and
        protected sessions. Administrative debug features may be enabled for authorized staff to resolve issues. Despite
        these measures, no system is completely secure.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        Your Choices and Responsibilities
      </Typography>
      <Typography variant="body1" paragraph>
        Use the Portal only for authorized purposes, keep your credentials confidential, and report suspected incidents to IT
        immediately. If you believe your information is inaccurate or requires update, contact IT.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        Changes to This Policy
      </Typography>
      <Typography variant="body1" paragraph>
        We may update this Privacy Policy as services evolve. Material changes will be communicated through the Portal or
        other channels.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        Contact
      </Typography>
      <Typography variant="body1" paragraph>
        For privacy questions, contact the CSF IT department.
      </Typography>

      <Divider sx={{ my: 3 }} />

      <Typography variant="body2">
        See also our{' '}
        <Link component={RouterLink} to="/terms">
          Terms of Service
        </Link>.
      </Typography>
    </Container>
  );
};

export default PrivacyPolicy;
