import React from 'react';
import { Container, Box, Typography, Link, List, ListItem, ListItemText, Divider } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';

const TermsOfService = () => {
  const lastUpdated = 'August 27, 2025';

  return (
    <Container maxWidth="md" sx={{ py: 6 }}>
      <Box sx={{ mb: 3 }}>
        <Typography variant="h3" component="h1" gutterBottom>
          Terms of Service
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Last updated: {lastUpdated}
        </Typography>
      </Box>

      <Typography variant="body1" paragraph>
        These Terms of Service ("Terms") govern your access to and use of the CSF Staff Portal (the "Portal").
        By accessing or using the Portal, you agree to be bound by these Terms. If you do not agree, do not use the Portal.
      </Typography>

      <Divider sx={{ my: 3 }} />

      <Typography variant="h5" component="h2" gutterBottom>
        1. Eligibility and Acceptable Use
      </Typography>
      <Typography variant="body1" paragraph>
        The Portal is an internal system intended for authorized CSF staff and designated users only. You must use your
        assigned CSF account and follow CSF policies. You agree not to misuse the Portal, including but not limited to
        attempting unauthorized access, interfering with operations, or using the Portal for unlawful activities.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        2. Accounts and Security
      </Typography>
      <Typography variant="body1" paragraph>
        You are responsible for maintaining the confidentiality of your credentials and for all activities under your account.
        Notify IT immediately of any suspected compromise. The Portal uses session cookies and may require Google sign-in or
        credentials-based authentication. For some users, additional factors (2FA) may be required.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        3. Third-party Integrations
      </Typography>
      <Typography variant="body1" paragraph>
        The Portal integrates with systems such as Google Workspace (Drive, Calendar, Admin, Forms), UniFi Network/Protect,
        UniFi Access, Lenel S2/NetBox, Synology, Planning Center, RADIUS, Gmail, LG ThinQ, Dreo, Rain Bird, and others to
        provide services. Your use of those services is also subject to the applicable third-party terms.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        4. Content and Data
      </Typography>
      <Typography variant="body1" paragraph>
        You agree that data accessed via the Portal (including files, staff profiles, tickets, logs, facility devices, and
        integrations) is confidential and for authorized CSF purposes only. Do not export, disclose, or redistribute
        confidential data without authorization.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        5. Service Availability and Changes
      </Typography>
      <Typography variant="body1" paragraph>
        The Portal may be updated, suspended, or discontinued at any time for maintenance, security, or operational reasons.
        Features and integrations may change without notice. We try to minimize disruption, but uptime is not guaranteed.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        6. Security
      </Typography>
      <Typography variant="body1" paragraph>
        The Portal employs security measures such as HTTPS, HTTP-only cookies, Content Security Policy (CSP), and role-based
        access controls. Administrative and diagnostic features (e.g., WebSocket debug logging) may be enabled in development
        or by authorized staff to troubleshoot issues.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        7. Compliance and Monitoring
      </Typography>
      <Typography variant="body1" paragraph>
        Use of the Portal may be monitored and logged to ensure compliance with CSF policies and to safeguard systems. You
        consent to such monitoring in accordance with our Privacy Policy.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        8. Termination of Access
      </Typography>
      <Typography variant="body1" paragraph>
        We may suspend or terminate access to the Portal for policy violations, security concerns, or changes in employment
        status. Upon termination, you must cease use and return or delete any confidential information as directed.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        9. Disclaimers and Limitation of Liability
      </Typography>
      <Typography variant="body1" paragraph>
        The Portal is provided on an "as is" and "as available" basis. To the maximum extent permitted by law, CSF disclaims
        all warranties and will not be liable for indirect, incidental, special, consequential, or punitive damages arising
        from your use of the Portal or third-party integrations.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        10. Changes to These Terms
      </Typography>
      <Typography variant="body1" paragraph>
        We may update these Terms from time to time. Material changes will be communicated via the Portal or other channels.
        Your continued use of the Portal after changes take effect constitutes acceptance of the updated Terms.
      </Typography>

      <Typography variant="h5" component="h2" gutterBottom>
        11. Contact
      </Typography>
      <Typography variant="body1" paragraph>
        For questions about these Terms, contact the CSF IT department.
      </Typography>

      <Divider sx={{ my: 3 }} />

      <Typography variant="body2">
        See also our{' '}
        <Link component={RouterLink} to="/privacy">
          Privacy Policy
        </Link>.
      </Typography>
    </Container>
  );
};

export default TermsOfService;
