// Static menu configuration for client-side menus (no DB)
// Icons are string names matching iconMap in Layout.js/AppsPage.js

// Helper to build a consistent id from path
const idFromPath = (path) =>
  path.replace(/^\//, '').replace(/\//g, '-').replace(/[^a-z0-9\-]/gi, '').toLowerCase() || 'root';

export const staticMenuItems = [
  // Regular (main) items
  { _id: idFromPath('/dashboard'), type: 'regular', text: 'Dashboard', icon: 'dashboard', path: '/dashboard', auth: true, categories: ['Core Features'], order: 0 },
  { _id: idFromPath('/shortcuts'), type: 'regular', text: 'Shortcuts', icon: 'link', path: '/shortcuts', auth: true, permission: 'shortcuts:read', categories: ['Core Features'] },
  { _id: idFromPath('/news'), type: 'regular', text: 'News', icon: 'article', path: '/news', auth: true, permission: 'news:read', categories: ['Core Features'] },
  { _id: idFromPath('/tickets'), type: 'regular', text: 'Help Tickets', icon: 'confirmation_number', path: '/tickets', auth: true, permission: 'tickets:read', categories: ['Help'] },
  { _id: idFromPath('/forms'), type: 'regular', text: 'Forms', icon: 'list', path: '/forms', auth: true, permission: 'forms:read', categories: ['Core Features'] },
  { _id: idFromPath('/drive'), type: 'regular', text: 'Drive Files', icon: 'folder', path: '/drive', auth: true, permission: 'googleDrive:read', categories: ['Google Services'] },
  { _id: idFromPath('/google-calendar'), type: 'regular', text: 'Google Calendar', icon: 'calendar_month', path: '/google-calendar', auth: true, permission: 'googleCalendar:read', categories: ['Google Services'] },
  { _id: idFromPath('/google-forms'), type: 'regular', text: 'Google Forms', icon: 'description', path: '/google-forms', auth: true, permission: 'googleForms:read', categories: ['Google Services'] },
  { _id: idFromPath('/people'), type: 'regular', text: 'People', icon: 'people', path: '/people', auth: true, permission: 'people:read' },
  { _id: idFromPath('/tasks'), type: 'regular', text: 'Tasks', icon: 'assignment', path: '/tasks', auth: true, permission: 'tasks:read' },
  { _id: idFromPath('/asset-management'), type: 'regular', text: 'Asset Management (Legacy)', icon: 'inventory', path: '/asset-management', auth: true, permission: 'assets:read', categories: ['Assets'] },
  { _id: idFromPath('/assets'), type: 'regular', text: 'Assets', icon: 'inventory2', path: '/assets', auth: true, permission: 'assets:read', categories: ['Assets'] },
  { _id: idFromPath('/assets/list'), type: 'regular', text: 'Asset List', icon: 'list', path: '/assets/list', auth: true, permission: 'assets:read', categories: ['Assets'] },
  { _id: idFromPath('/assets/reports'), type: 'regular', text: 'Asset Reports', icon: 'assessment', path: '/assets/reports', auth: true, permission: 'assets:read', categories: ['Assets'] },
  { _id: idFromPath('/assets/import-export'), type: 'regular', text: 'Asset Import/Export', icon: 'import_export', path: '/assets/import-export', auth: true, permission: 'assets:admin', categories: ['Assets'] },
  { _id: idFromPath('/notes'), type: 'regular', text: 'Notes', icon: 'note', path: '/notes', auth: true, permission: 'notes:read' },
  { _id: idFromPath('/room-booking'), type: 'regular', text: 'Room Booking', icon: 'meeting_room', path: '/room-booking', auth: true, permission: 'roomBooking:read' },
  { _id: idFromPath('/building-management'), type: 'regular', text: 'Building Management', icon: 'home_work', path: '/building-management', auth: true, permission: 'buildingManagement:read' },
  { _id: idFromPath('/help'), type: 'regular', text: 'Help Center', icon: 'help_outline', path: '/help', auth: true, permission: 'help:read', categories: ['Help'] },
  { _id: idFromPath('/help/faq'), type: 'regular', text: 'FAQs', icon: 'question_answer', path: '/help/faq', auth: true, permission: 'help:read', categories: ['Help'] },

  // Integration items
  { _id: 'canva', type: 'integration', text: 'Canva', icon: 'image', path: '/canva', auth: true, permission: 'canva:read', categories: ['Integrations'] },
  { _id: 'planning-center', type: 'integration', text: 'Planning Center', icon: 'event', path: '/planning-center', auth: true, permission: 'planningCenter:read', categories: ['Integrations'] },
  { _id: 'synology', type: 'integration', text: 'Synology', icon: 'storage', path: '/synology', auth: true, permission: 'synology:read', categories: ['Integrations'] },
  { _id: 'dreo', type: 'integration', text: 'Dreo', icon: 'ac_unit', path: '/dreo', auth: true, permission: 'dreo:read', categories: ['Integrations'] },
  { _id: 'lg-thinq', type: 'integration', text: 'LG ThinQ AC', icon: 'ac_unit', path: '/lg-thinq', auth: true, permission: 'lgThinq:read', categories: ['Integrations'] },
  { _id: 'lenel-s2-netbox', type: 'integration', text: 'Lenel S2 NetBox', icon: 'security', path: '/lenel-s2-netbox', auth: true, permission: 'lenelS2NetBox:read', categories: ['Integrations'] },
  { _id: 'mosyle-business', type: 'integration', text: 'Mosyle Business', icon: 'phone_iphone', path: '/mosyle-business', auth: true, permission: 'mosyleBusiness:read', categories: ['Integrations'] },
  { _id: 'unifi-access', type: 'integration', text: 'UniFi Access', icon: 'lock', path: '/unifi-access', auth: true, permission: 'unifiAccess:read', categories: ['Integrations'] },
  { _id: 'unifi-network', type: 'integration', text: 'UniFi Network', icon: 'router', path: '/unifi-network', auth: true, permission: 'unifiNetwork:read', categories: ['Integrations'] },
  { _id: 'unifi-protect', type: 'integration', text: 'UniFi Protect', icon: 'videocam', path: '/unifi-protect', auth: true, permission: 'unifiProtect:read', categories: ['Integrations'] },
  { _id: 'google-admin', type: 'integration', text: 'Google Admin', icon: 'admin_panel_settings', path: '/google-admin', auth: true, permission: 'googleAdmin:read', categories: ['Google Services', 'Integrations'] },
  { _id: 'google-forms', type: 'integration', text: 'Google Forms', icon: 'description', path: '/google-forms', auth: true, permission: 'googleForms:read', categories: ['Google Services', 'Integrations'] },
  { _id: 'radius', type: 'integration', text: 'RADIUS', icon: 'network_check', path: '/radius', auth: true, permission: 'radius:read', categories: ['Integrations'] },
  { _id: 'apple-business-manager', type: 'integration', text: 'Apple Business Manager', icon: 'apple', path: '/apple-business-manager', auth: true, permission: 'appleBusinessManager:read', categories: ['Integrations'] },
  { _id: 'rain-bird', type: 'integration', text: 'Rain Bird', icon: 'thermostat', path: '/rain-bird', auth: true, permission: 'rainBird:read', categories: ['Integrations'] },
  { _id: 'wiim', type: 'integration', text: 'WiiM', icon: 'music_note', path: '/wiim', auth: true, permission: 'wiim:read', categories: ['Integrations'] },
  { _id: 'skyportcloud', type: 'integration', text: 'SkyportCloud HVAC', icon: 'thermostat', path: '/skyportcloud', auth: true, permission: 'skyportcloud:read', categories: ['Integrations'] },
  { _id: 'qsys', type: 'integration', text: 'Q-sys Core Manager', icon: 'speaker', path: '/qsys', auth: true, permission: 'qsys:read', categories: ['Integrations'] },
  { _id: 'colorlit', type: 'integration', text: 'Colorlit LED Controller', icon: 'lightbulb', path: '/colorlit', auth: true, permission: 'colorlit:read', categories: ['Integrations'] },
  { _id: 'zeevee', type: 'integration', text: 'ZeeVee HDbridge', icon: 'tv', path: '/zeevee', auth: true, permission: 'zeevee:read', categories: ['Integrations'] },
  { _id: 'panasonic', type: 'integration', text: 'Panasonic Pro AV Camera', icon: 'videocam', path: '/panasonic', auth: true, permission: 'panasonic:read', categories: ['Integrations'] },

  // Admin items
  { _id: idFromPath('/admin/status'), type: 'admin', text: 'System Status', icon: 'admin_panel_settings', path: '/admin/status', admin: true },
  { _id: idFromPath('/admin/users'), type: 'admin', text: 'Manage Users', icon: 'people', path: '/admin/users', admin: true, permission: 'users:admin' },
  { _id: idFromPath('/admin/roles'), type: 'admin', text: 'Manage Roles', icon: 'vpn_key', path: '/admin/roles', admin: true, permission: 'roles:admin' },
  { _id: idFromPath('/admin/shortcuts'), type: 'admin', text: 'Manage Shortcuts', icon: 'settings', path: '/admin/shortcuts', admin: true, permission: 'shortcuts:admin' },
  { _id: idFromPath('/admin/menu'), type: 'admin', text: 'Manage Menu', icon: 'menu', path: '/admin/menu', admin: true, permission: 'menu:admin' },
  { _id: idFromPath('/admin/radius'), type: 'admin', text: 'Manage RADIUS', icon: 'network_check', path: '/admin/radius', admin: true, permission: 'radius:admin' },
  { _id: idFromPath('/admin/building-management'), type: 'admin', text: 'Manage Building Management', icon: 'home_work', path: '/admin/building-management', admin: true, permission: 'buildingManagement:admin' },
  { _id: idFromPath('/admin/ticket-categories-and-tags'), type: 'admin', text: 'Manage Ticket Categories & Tags', icon: 'category', path: '/admin/ticket-categories-and-tags', admin: true, permission: 'tickets:admin' },
  { _id: idFromPath('/access-control'), type: 'admin', text: 'Access Control', icon: 'security', path: '/access-control', admin: true, permission: 'accessControl:admin' }
];

export const staticMenuCategories = [
  { _id: 'core-features', name: 'Core Features', description: 'Built-in features', icon: 'apps', color: '#1976d2', order: 0 },
  { _id: 'google-services', name: 'Google Services', description: 'Google integrations', icon: 'calendar_month', color: '#4285F4', order: 10 },
  { _id: 'assets', name: 'Assets', description: 'Asset management', icon: 'inventory2', color: '#6d4c41', order: 20 },
  { _id: 'integrations', name: 'Integrations', description: 'External systems', icon: 'apps', color: '#9e9e9e', order: 30 },
  { _id: 'help', name: 'Help', description: 'Help and docs', icon: 'help_outline', color: '#7cb342', order: 40 }
];

export default { staticMenuItems, staticMenuCategories };
