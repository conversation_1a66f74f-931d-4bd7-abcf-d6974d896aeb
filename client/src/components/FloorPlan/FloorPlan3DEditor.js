import React, { useState, useRef, useEffect } from 'react';
import * as THREE from 'three';
import {
  Box,
  Paper,
  Typography,
  Button,
  ButtonGroup,
  IconButton,
  Slider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  ToggleButton,
  ToggleButtonGroup,
  Tooltip,
  Grid,
  Alert
} from '@mui/material';
import {
  ViewInAr,
  Wallpaper,
  DoorFront,
  Window,
  Weekend,
  Height,
  Delete,
  Edit,
  Save,
  Undo,
  Redo,
  CropFree,
  ZoomIn,
  ZoomOut,
  GridOn,
  Straighten,
  Architecture,
  ThreeDRotation,
  Layers,
  Chair,
  Bed,
  TableRestaurant,
  Tv,
  Kitchen,
  Bathtub
} from '@mui/icons-material';

const GRID_SIZE = 0.5;
const WALL_HEIGHT = 3;
const WALL_THICKNESS = 0.15;
const DOOR_WIDTH = 0.9;
const DOOR_HEIGHT = 2.1;
const WINDOW_WIDTH = 1.2;
const WINDOW_HEIGHT = 1.2;
const WINDOW_SILL_HEIGHT = 0.9;

const FURNITURE_LIBRARY = {
  seating: [
    { id: 'chair', name: 'Chair', icon: Chair, dimensions: { width: 0.6, depth: 0.6, height: 0.9 } },
    { id: 'sofa', name: 'Sofa', icon: Weekend, dimensions: { width: 2.0, depth: 0.9, height: 0.8 } },
    { id: 'armchair', name: 'Armchair', icon: Chair, dimensions: { width: 0.8, depth: 0.8, height: 0.9 } }
  ],
  tables: [
    { id: 'dining-table', name: 'Dining Table', icon: TableRestaurant, dimensions: { width: 1.5, depth: 0.9, height: 0.75 } },
    { id: 'coffee-table', name: 'Coffee Table', icon: TableRestaurant, dimensions: { width: 1.2, depth: 0.6, height: 0.4 } },
    { id: 'desk', name: 'Desk', icon: TableRestaurant, dimensions: { width: 1.4, depth: 0.7, height: 0.75 } }
  ],
  bedroom: [
    { id: 'single-bed', name: 'Single Bed', icon: Bed, dimensions: { width: 1.0, depth: 2.0, height: 0.5 } },
    { id: 'double-bed', name: 'Double Bed', icon: Bed, dimensions: { width: 1.5, depth: 2.0, height: 0.5 } },
    { id: 'queen-bed', name: 'Queen Bed', icon: Bed, dimensions: { width: 1.6, depth: 2.0, height: 0.5 } }
  ],
  appliances: [
    { id: 'tv', name: 'TV', icon: Tv, dimensions: { width: 1.2, depth: 0.05, height: 0.7 } },
    { id: 'refrigerator', name: 'Refrigerator', icon: Kitchen, dimensions: { width: 0.7, depth: 0.7, height: 1.8 } },
    { id: 'stove', name: 'Stove', icon: Kitchen, dimensions: { width: 0.6, depth: 0.6, height: 0.9 } }
  ],
  bathroom: [
    { id: 'toilet', name: 'Toilet', icon: Bathtub, dimensions: { width: 0.4, depth: 0.7, height: 0.8 } },
    { id: 'bathtub', name: 'Bathtub', icon: Bathtub, dimensions: { width: 1.7, depth: 0.7, height: 0.6 } },
    { id: 'sink', name: 'Sink', icon: Bathtub, dimensions: { width: 0.6, depth: 0.5, height: 0.9 } }
  ]
};

const FloorPlan3DEditor = ({ floor, onSave, onClose }) => {
  const mountRef = useRef(null);
  const sceneRef = useRef(null);
  const rendererRef = useRef(null);
  const cameraRef = useRef(null);
  const controlsRef = useRef(null);
  const raycasterRef = useRef(new THREE.Raycaster());
  const mouseRef = useRef(new THREE.Vector2());
  
  const [mode, setMode] = useState('view');
  const [viewMode, setViewMode] = useState('2d');
  const [selectedTool, setSelectedTool] = useState('wall');
  const [ceilingHeight, setCeilingHeight] = useState(WALL_HEIGHT);
  const [showGrid, setShowGrid] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [selectedObject, setSelectedObject] = useState(null);
  const [selectedFurniture, setSelectedFurniture] = useState(null);
  const [objects, setObjects] = useState({
    walls: [],
    doors: [],
    windows: [],
    furniture: []
  });
  const [history, setHistory] = useState([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isDrawing, setIsDrawing] = useState(false);
  const [drawingStart, setDrawingStart] = useState(null);
  const [previewLine, setPreviewLine] = useState(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingObject, setEditingObject] = useState(null);
  const [furnitureDialogOpen, setFurnitureDialogOpen] = useState(false);
  
  useEffect(() => {
    initThreeJS();
    loadExisting3DData();
    
    return () => {
      if (rendererRef.current) {
        rendererRef.current.dispose();
      }
    };
  }, []);
  
  const loadExisting3DData = async () => {
    try {
      const response = await fetch(`/api/floors/${floor._id}/3dplan`);
      if (response.ok) {
        const data = await response.json();
        if (data.objects) {
          setObjects(data.objects);
        }
        if (data.ceilingHeight) {
          setCeilingHeight(data.ceilingHeight);
        }
      }
    } catch (error) {
      console.log('No existing 3D data found');
    }
  };
  
  useEffect(() => {
    if (viewMode === '3d') {
      setup3DView();
    } else {
      setup2DView();
    }
  }, [viewMode]);
  
  useEffect(() => {
    updateScene();
  }, [objects, ceilingHeight, showGrid, viewMode]);
  
  const initThreeJS = () => {
    const width = mountRef.current.clientWidth;
    const height = mountRef.current.clientHeight;
    
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf0f0f0);
    sceneRef.current = scene;
    
    const camera = new THREE.PerspectiveCamera(
      75,
      width / height,
      0.1,
      1000
    );
    camera.position.set(10, 10, 10);
    camera.lookAt(0, 0, 0);
    cameraRef.current = camera;
    
    const renderer = new THREE.WebGLRenderer({ antialias: true });
    renderer.setSize(width, height);
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    mountRef.current.appendChild(renderer.domElement);
    rendererRef.current = renderer;
    
    const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
    scene.add(ambientLight);
    
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.4);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.camera.near = 0.1;
    directionalLight.shadow.camera.far = 50;
    directionalLight.shadow.camera.left = -20;
    directionalLight.shadow.camera.right = 20;
    directionalLight.shadow.camera.top = 20;
    directionalLight.shadow.camera.bottom = -20;
    scene.add(directionalLight);
    
    setupControls();
    animate();
  };
  
  const setupControls = () => {
    if (!rendererRef.current) return;
    
    const OrbitControls = require('three/examples/jsm/controls/OrbitControls').OrbitControls;
    const controls = new OrbitControls(cameraRef.current, rendererRef.current.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.05;
    controls.maxPolarAngle = Math.PI / 2;
    controlsRef.current = controls;
  };
  
  const setup2DView = () => {
    if (!cameraRef.current) return;
    cameraRef.current.position.set(0, 20, 0);
    cameraRef.current.lookAt(0, 0, 0);
    if (controlsRef.current) {
      controlsRef.current.enableRotate = false;
      controlsRef.current.update();
    }
  };
  
  const setup3DView = () => {
    if (!cameraRef.current) return;
    cameraRef.current.position.set(15, 10, 15);
    cameraRef.current.lookAt(0, 0, 0);
    if (controlsRef.current) {
      controlsRef.current.enableRotate = true;
      controlsRef.current.update();
    }
  };
  
  const animate = () => {
    requestAnimationFrame(animate);
    
    if (controlsRef.current) {
      controlsRef.current.update();
    }
    
    if (rendererRef.current && sceneRef.current && cameraRef.current) {
      rendererRef.current.render(sceneRef.current, cameraRef.current);
    }
  };
  
  const updateScene = () => {
    if (!sceneRef.current) return;
    
    while(sceneRef.current.children.length > 2) {
      const child = sceneRef.current.children[2];
      sceneRef.current.remove(child);
      if (child.geometry) child.geometry.dispose();
      if (child.material) child.material.dispose();
    }
    
    if (showGrid) {
      const gridHelper = new THREE.GridHelper(20, 40, 0x888888, 0xcccccc);
      sceneRef.current.add(gridHelper);
    }
    
    const floorGeometry = new THREE.PlaneGeometry(20, 20);
    const floorMaterial = new THREE.MeshStandardMaterial({ 
      color: 0xeeeeee,
      roughness: 0.8,
      metalness: 0.2
    });
    const floorMesh = new THREE.Mesh(floorGeometry, floorMaterial);
    floorMesh.rotation.x = -Math.PI / 2;
    floorMesh.receiveShadow = true;
    sceneRef.current.add(floorMesh);
    
    objects.walls.forEach(wall => {
      const wallMesh = createWallMesh(wall);
      sceneRef.current.add(wallMesh);
    });
    
    objects.doors.forEach(door => {
      const doorMesh = createDoorMesh(door);
      sceneRef.current.add(doorMesh);
    });
    
    objects.windows.forEach(window => {
      const windowMesh = createWindowMesh(window);
      sceneRef.current.add(windowMesh);
    });
    
    objects.furniture.forEach(item => {
      const furnitureMesh = createFurnitureMesh(item);
      sceneRef.current.add(furnitureMesh);
    });
    
    if (previewLine) {
      const lineMaterial = new THREE.LineBasicMaterial({ 
        color: 0x00ff00,
        linewidth: 2
      });
      const lineGeometry = new THREE.BufferGeometry().setFromPoints([
        new THREE.Vector3(previewLine.start.x, 0.1, previewLine.start.z),
        new THREE.Vector3(previewLine.end.x, 0.1, previewLine.end.z)
      ]);
      const line = new THREE.Line(lineGeometry, lineMaterial);
      sceneRef.current.add(line);
    }
  };
  
  const createWallMesh = (wall) => {
    const length = Math.sqrt(
      Math.pow(wall.end.x - wall.start.x, 2) + 
      Math.pow(wall.end.z - wall.start.z, 2)
    );
    
    const geometry = new THREE.BoxGeometry(length, wall.height || ceilingHeight, WALL_THICKNESS);
    const material = new THREE.MeshStandardMaterial({ 
      color: wall.color || 0x8B7355,
      roughness: 0.7,
      metalness: 0.1
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(
      (wall.start.x + wall.end.x) / 2,
      (wall.height || ceilingHeight) / 2,
      (wall.start.z + wall.end.z) / 2
    );
    
    const angle = Math.atan2(wall.end.z - wall.start.z, wall.end.x - wall.start.x);
    mesh.rotation.y = angle;
    
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    mesh.userData = { type: 'wall', data: wall };
    
    return mesh;
  };
  
  const createDoorMesh = (door) => {
    const frameGeometry = new THREE.BoxGeometry(DOOR_WIDTH, DOOR_HEIGHT, 0.1);
    const frameMaterial = new THREE.MeshStandardMaterial({ color: 0x654321 });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    
    const doorGeometry = new THREE.BoxGeometry(DOOR_WIDTH - 0.1, DOOR_HEIGHT - 0.1, 0.05);
    const doorMaterial = new THREE.MeshStandardMaterial({ 
      color: 0x8B4513,
      roughness: 0.5,
      metalness: 0.1
    });
    const doorPanel = new THREE.Mesh(doorGeometry, doorMaterial);
    doorPanel.position.z = 0.025;
    
    const group = new THREE.Group();
    group.add(frame);
    group.add(doorPanel);
    
    group.position.set(door.position.x, DOOR_HEIGHT / 2, door.position.z);
    group.rotation.y = door.rotation || 0;
    
    group.castShadow = true;
    group.userData = { type: 'door', data: door };
    
    return group;
  };
  
  const createWindowMesh = (window) => {
    const frameGeometry = new THREE.BoxGeometry(WINDOW_WIDTH, WINDOW_HEIGHT, 0.1);
    const frameMaterial = new THREE.MeshStandardMaterial({ color: 0x333333 });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    
    const glassGeometry = new THREE.BoxGeometry(WINDOW_WIDTH - 0.1, WINDOW_HEIGHT - 0.1, 0.02);
    const glassMaterial = new THREE.MeshStandardMaterial({ 
      color: 0x87CEEB,
      transparent: true,
      opacity: 0.3,
      roughness: 0.1,
      metalness: 0.9
    });
    const glass = new THREE.Mesh(glassGeometry, glassMaterial);
    
    const group = new THREE.Group();
    group.add(frame);
    group.add(glass);
    
    group.position.set(
      window.position.x,
      WINDOW_SILL_HEIGHT + WINDOW_HEIGHT / 2,
      window.position.z
    );
    group.rotation.y = window.rotation || 0;
    
    group.userData = { type: 'window', data: window };
    
    return group;
  };
  
  const createFurnitureMesh = (item) => {
    const furnitureInfo = getFurnitureInfo(item.furnitureId);
    if (!furnitureInfo) return new THREE.Group();
    
    const geometry = new THREE.BoxGeometry(
      furnitureInfo.dimensions.width,
      furnitureInfo.dimensions.height,
      furnitureInfo.dimensions.depth
    );
    
    const material = new THREE.MeshStandardMaterial({ 
      color: item.color || 0x666666,
      roughness: 0.6,
      metalness: 0.2
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    mesh.position.set(
      item.position.x,
      furnitureInfo.dimensions.height / 2,
      item.position.z
    );
    mesh.rotation.y = item.rotation || 0;
    
    mesh.castShadow = true;
    mesh.receiveShadow = true;
    mesh.userData = { type: 'furniture', data: item };
    
    return mesh;
  };
  
  const getFurnitureInfo = (furnitureId) => {
    for (const category of Object.values(FURNITURE_LIBRARY)) {
      const item = category.find(f => f.id === furnitureId);
      if (item) return item;
    }
    return null;
  };
  
  const handleCanvasClick = (event) => {
    if (mode === 'view' || viewMode === '3d') return;
    
    const rect = mountRef.current.getBoundingClientRect();
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    
    raycasterRef.current.setFromCamera(mouseRef.current, cameraRef.current);
    
    const planeGeometry = new THREE.PlaneGeometry(100, 100);
    const planeMaterial = new THREE.MeshBasicMaterial({ visible: false });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = -Math.PI / 2;
    
    const intersects = raycasterRef.current.intersectObject(plane);
    
    if (intersects.length > 0) {
      const point = intersects[0].point;
      
      if (snapToGrid) {
        point.x = Math.round(point.x / GRID_SIZE) * GRID_SIZE;
        point.z = Math.round(point.z / GRID_SIZE) * GRID_SIZE;
      }
      
      handleToolAction(point);
    }
  };
  
  const handleToolAction = (point) => {
    switch (selectedTool) {
      case 'wall':
        handleWallTool(point);
        break;
      case 'door':
        placeDoor(point);
        break;
      case 'window':
        placeWindow(point);
        break;
      case 'furniture':
        if (selectedFurniture) {
          placeFurniture(point);
        }
        break;
      default:
        break;
    }
  };
  
  const handleWallTool = (point) => {
    if (!isDrawing) {
      setIsDrawing(true);
      setDrawingStart({ x: point.x, z: point.z });
      setPreviewLine({
        start: { x: point.x, z: point.z },
        end: { x: point.x, z: point.z }
      });
    } else {
      const newWall = {
        id: Date.now(),
        start: drawingStart,
        end: { x: point.x, z: point.z },
        height: ceilingHeight,
        thickness: WALL_THICKNESS
      };
      
      addObject('walls', newWall);
      setIsDrawing(false);
      setDrawingStart(null);
      setPreviewLine(null);
    }
  };
  
  const placeDoor = (point) => {
    const newDoor = {
      id: Date.now(),
      position: { x: point.x, y: 0, z: point.z },
      rotation: 0,
      width: DOOR_WIDTH,
      height: DOOR_HEIGHT
    };
    
    addObject('doors', newDoor);
  };
  
  const placeWindow = (point) => {
    const newWindow = {
      id: Date.now(),
      position: { x: point.x, y: 0, z: point.z },
      rotation: 0,
      width: WINDOW_WIDTH,
      height: WINDOW_HEIGHT
    };
    
    addObject('windows', newWindow);
  };
  
  const placeFurniture = (point) => {
    if (!selectedFurniture) return;
    
    const furnitureInfo = getFurnitureInfo(selectedFurniture);
    const newFurniture = {
      id: Date.now(),
      furnitureId: selectedFurniture,
      position: { x: point.x, y: 0, z: point.z },
      rotation: 0,
      color: 0x666666
    };
    
    addObject('furniture', newFurniture);
  };
  
  const addObject = (type, object) => {
    const newObjects = {
      ...objects,
      [type]: [...objects[type], object]
    };
    
    setObjects(newObjects);
    addToHistory(newObjects);
  };
  
  const removeObject = (type, objectId) => {
    const newObjects = {
      ...objects,
      [type]: objects[type].filter(obj => obj.id !== objectId)
    };
    
    setObjects(newObjects);
    addToHistory(newObjects);
  };
  
  const updateObject = (type, objectId, updates) => {
    const newObjects = {
      ...objects,
      [type]: objects[type].map(obj => 
        obj.id === objectId ? { ...obj, ...updates } : obj
      )
    };
    
    setObjects(newObjects);
    addToHistory(newObjects);
  };
  
  const addToHistory = (newState) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(newState);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };
  
  const handleUndo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setObjects(history[historyIndex - 1]);
    }
  };
  
  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setObjects(history[historyIndex + 1]);
    }
  };
  
  const handleMouseMove = (event) => {
    if (!isDrawing || viewMode === '3d') return;
    
    const rect = mountRef.current.getBoundingClientRect();
    mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
    mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;
    
    raycasterRef.current.setFromCamera(mouseRef.current, cameraRef.current);
    
    const planeGeometry = new THREE.PlaneGeometry(100, 100);
    const planeMaterial = new THREE.MeshBasicMaterial({ visible: false });
    const plane = new THREE.Mesh(planeGeometry, planeMaterial);
    plane.rotation.x = -Math.PI / 2;
    
    const intersects = raycasterRef.current.intersectObject(plane);
    
    if (intersects.length > 0) {
      const point = intersects[0].point;
      
      if (snapToGrid) {
        point.x = Math.round(point.x / GRID_SIZE) * GRID_SIZE;
        point.z = Math.round(point.z / GRID_SIZE) * GRID_SIZE;
      }
      
      setPreviewLine({
        start: drawingStart,
        end: { x: point.x, z: point.z }
      });
    }
  };
  
  const handleSave = () => {
    const floorplanData = {
      ceilingHeight,
      objects,
      metadata: {
        createdAt: new Date().toISOString(),
        version: '1.0'
      }
    };
    
    if (onSave) {
      onSave(floorplanData);
    }
  };
  
  const openEditDialog = (object) => {
    setEditingObject(object);
    setEditDialogOpen(true);
  };
  
  const handleEditSave = (updates) => {
    if (editingObject) {
      updateObject(editingObject.type, editingObject.data.id, updates);
    }
    setEditDialogOpen(false);
    setEditingObject(null);
  };
  
  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column', bgcolor: '#f5f5f5' }}>
      <Paper elevation={2} sx={{ p: 2, mb: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <ToggleButtonGroup
              value={viewMode}
              exclusive
              onChange={(e, newMode) => newMode && setViewMode(newMode)}
              size="small"
            >
              <ToggleButton value="2d">
                <Tooltip title="2D View">
                  <Architecture />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="3d">
                <Tooltip title="3D View">
                  <ThreeDRotation />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
          </Grid>
          
          <Grid item>
            <Divider orientation="vertical" />
          </Grid>
          
          <Grid item>
            <ToggleButtonGroup
              value={mode}
              exclusive
              onChange={(e, newMode) => newMode && setMode(newMode)}
              size="small"
            >
              <ToggleButton value="view">
                <Tooltip title="View Mode">
                  <CropFree />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="edit">
                <Tooltip title="Edit Mode">
                  <Edit />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
          </Grid>
          
          {mode === 'edit' && (
            <>
              <Grid item>
                <Divider orientation="vertical" />
              </Grid>
              
              <Grid item>
                <ButtonGroup size="small">
                  <Button
                    variant={selectedTool === 'wall' ? 'contained' : 'outlined'}
                    onClick={() => setSelectedTool('wall')}
                    startIcon={<Wallpaper />}
                  >
                    Wall
                  </Button>
                  <Button
                    variant={selectedTool === 'door' ? 'contained' : 'outlined'}
                    onClick={() => setSelectedTool('door')}
                    startIcon={<DoorFront />}
                  >
                    Door
                  </Button>
                  <Button
                    variant={selectedTool === 'window' ? 'contained' : 'outlined'}
                    onClick={() => setSelectedTool('window')}
                    startIcon={<Window />}
                  >
                    Window
                  </Button>
                  <Button
                    variant={selectedTool === 'furniture' ? 'contained' : 'outlined'}
                    onClick={() => {
                      setSelectedTool('furniture');
                      setFurnitureDialogOpen(true);
                    }}
                    startIcon={<Weekend />}
                  >
                    Furniture
                  </Button>
                </ButtonGroup>
              </Grid>
            </>
          )}
          
          <Grid item>
            <Divider orientation="vertical" />
          </Grid>
          
          <Grid item>
            <ButtonGroup size="small">
              <IconButton onClick={handleUndo} disabled={historyIndex <= 0}>
                <Undo />
              </IconButton>
              <IconButton onClick={handleRedo} disabled={historyIndex >= history.length - 1}>
                <Redo />
              </IconButton>
            </ButtonGroup>
          </Grid>
          
          <Grid item>
            <Divider orientation="vertical" />
          </Grid>
          
          <Grid item sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Height />
            <Typography variant="body2">Ceiling Height:</Typography>
            <TextField
              size="small"
              type="number"
              value={ceilingHeight}
              onChange={(e) => setCeilingHeight(parseFloat(e.target.value))}
              inputProps={{ min: 2.4, max: 6, step: 0.1 }}
              sx={{ width: 80 }}
            />
            <Typography variant="body2">m</Typography>
          </Grid>
          
          <Grid item>
            <ButtonGroup size="small">
              <Button
                variant={showGrid ? 'contained' : 'outlined'}
                onClick={() => setShowGrid(!showGrid)}
                startIcon={<GridOn />}
              >
                Grid
              </Button>
              <Button
                variant={snapToGrid ? 'contained' : 'outlined'}
                onClick={() => setSnapToGrid(!snapToGrid)}
                startIcon={<Straighten />}
              >
                Snap
              </Button>
            </ButtonGroup>
          </Grid>
          
          <Grid item sx={{ ml: 'auto' }}>
            <ButtonGroup>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSave}
                startIcon={<Save />}
              >
                Save
              </Button>
              {onClose && (
                <Button onClick={onClose}>
                  Close
                </Button>
              )}
            </ButtonGroup>
          </Grid>
        </Grid>
      </Paper>
      
      <Grid container spacing={2} sx={{ flexGrow: 1, overflow: 'hidden' }}>
        <Grid item xs={9}>
          <Paper
            ref={mountRef}
            elevation={3}
            sx={{
              height: '100%',
              position: 'relative',
              cursor: mode === 'edit' && viewMode === '2d' ? 'crosshair' : 'grab',
              overflow: 'hidden'
            }}
            onClick={handleCanvasClick}
            onMouseMove={handleMouseMove}
          />
        </Grid>
        
        <Grid item xs={3}>
          <Paper elevation={3} sx={{ height: '100%', p: 2, overflow: 'auto' }}>
            <Typography variant="h6" gutterBottom>
              Objects
            </Typography>
            <Divider sx={{ mb: 2 }} />
            
            {Object.entries(objects).map(([type, items]) => (
              <Box key={type} sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  {type.charAt(0).toUpperCase() + type.slice(1)} ({items.length})
                </Typography>
                <List dense>
                  {items.map((item) => (
                    <ListItem key={item.id}>
                      <ListItemIcon>
                        {type === 'walls' && <Wallpaper />}
                        {type === 'doors' && <DoorFront />}
                        {type === 'windows' && <Window />}
                        {type === 'furniture' && <Weekend />}
                      </ListItemIcon>
                      <ListItemText
                        primary={`${type.slice(0, -1)} #${item.id.toString().slice(-4)}`}
                        secondary={item.furnitureId || ''}
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          size="small"
                          onClick={() => removeObject(type, item.id)}
                        >
                          <Delete />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              </Box>
            ))}
            
            {Object.values(objects).every(arr => arr.length === 0) && (
              <Alert severity="info">
                No objects added yet. Switch to Edit mode and select a tool to start building.
              </Alert>
            )}
          </Paper>
        </Grid>
      </Grid>
      
      <Dialog
        open={furnitureDialogOpen}
        onClose={() => setFurnitureDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Select Furniture</DialogTitle>
        <DialogContent>
          {Object.entries(FURNITURE_LIBRARY).map(([category, items]) => (
            <Box key={category} sx={{ mb: 3 }}>
              <Typography variant="h6" gutterBottom>
                {category.charAt(0).toUpperCase() + category.slice(1)}
              </Typography>
              <Grid container spacing={2}>
                {items.map((item) => {
                  const Icon = item.icon;
                  return (
                    <Grid item xs={6} sm={4} md={3} key={item.id}>
                      <Button
                        fullWidth
                        variant={selectedFurniture === item.id ? 'contained' : 'outlined'}
                        onClick={() => setSelectedFurniture(item.id)}
                        sx={{
                          flexDirection: 'column',
                          py: 2,
                          height: '100%'
                        }}
                      >
                        <Icon sx={{ fontSize: 40, mb: 1 }} />
                        <Typography variant="body2">{item.name}</Typography>
                        <Typography variant="caption" color="text.secondary">
                          {item.dimensions.width}×{item.dimensions.depth}m
                        </Typography>
                      </Button>
                    </Grid>
                  );
                })}
              </Grid>
            </Box>
          ))}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setFurnitureDialogOpen(false)}>Close</Button>
          <Button
            variant="contained"
            onClick={() => {
              setFurnitureDialogOpen(false);
            }}
            disabled={!selectedFurniture}
          >
            Select
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FloorPlan3DEditor;