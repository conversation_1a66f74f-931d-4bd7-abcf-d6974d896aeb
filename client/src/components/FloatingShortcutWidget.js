import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Paper, 
  IconButton, 
  Tooltip, 
  Popover, 
  List, 
  ListItem, 
  ListItemIcon, 
  ListItemText, 
  ListItemButton,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Checkbox,
  FormControlLabel,
  TextField,
  InputAdornment,
  Tabs,
  Tab,
  AppBar,
  Toolbar,
  Drawer,
  Collapse,
  Switch,
  useMediaQuery
} from '@mui/material';
import { 
  Link as LinkIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  Add as AddIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  CloudUpload as CloudUploadIcon,
  CloudOff as CloudOffIcon,
  Dashboard as DashboardIcon,
  Folder as FolderIcon,
  People as PeopleIcon,
  Event as CalendarIcon,
  Description as FormsIcon,
  ContactPage as StaffDirectoryIcon,
  Assignment as TasksIcon,
  MeetingRoom as RoomBookingIcon,
  HomeWork as BuildingManagementIcon,
  HelpOutline as HelpIcon,
  QuestionAnswer as FAQIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Announcement as NewsIcon,
  Note as NotesIcon,
  Inventory as AssetManagementIcon,
  ConfirmationNumber as TicketsIcon,
  Palette as CanvaIcon,
  Storage as GLPIIcon,
  Storage as SynologyIcon,
  CalendarMonth as PlanningCenterIcon,
  Air as DreoIcon,
  Kitchen as LGThinqIcon,
  WaterDrop as RainBirdIcon,
  Security as LenelS2NetBoxIcon,
  Apple as MosyleBusinessIcon,
  Lock as UnifiAccessIcon,
  AdminPanelSettings as GoogleAdminIcon,
  Router as UnifiNetworkIcon,
  Videocam as UnifiProtectIcon,
  Business as AppleBusinessManagerIcon,
  PhoneAndroid as PhoneBookIcon,
  Wifi as RadiusIcon,
  MusicNote as WiimIcon,
  Thermostat as SkyportCloudIcon,
  Speaker as QsysIcon,
  Lightbulb as ColoritIcon,
  SettingsInputComponent as ZeeVeeIcon,
  Tv as PanasonicIcon,
  VpnKey as AccessControlIcon,
  Menu as MenuIcon,
  Key as VpnKeyIcon
} from '@mui/icons-material';
import axios from 'axios';
import { useTheme } from '@mui/material/styles';
import { useAuth } from '../context/AuthContext';
import { Link as RouterLink } from 'react-router-dom';

const FloatingShortcutWidget = ({ inHeader = false }) => {
  // Get auth context for user role information
  const { user, hasPermission, isAdmin } = useAuth();
  
  // State for the widget
  const [expanded, setExpanded] = useState(false); // Start collapsed by default
  const [shortcuts, setShortcuts] = useState([]);
  const [portalPages, setPortalPages] = useState([]);
  const [favoriteShortcuts, setFavoriteShortcuts] = useState([]);
  const [favoritePortalPages, setFavoritePortalPages] = useState([]);
  const [widgetEnabled, setWidgetEnabled] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [editMode, setEditMode] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [savingToDb, setSavingToDb] = useState(false);
  const [saveError, setSaveError] = useState(null);
  const [tabValue, setTabValue] = useState(0); // 0 for shortcuts, 1 for portal pages
  const [alwaysShowLabels, setAlwaysShowLabels] = useState(false); // New state for label visibility
  
  // Responsive behavior: align with Layout's desktop sidebar
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('md'));
  const drawerWidth = 250;
  
  // Define portal pages with their role requirements
  const portalPagesList = [
    { id: 'dashboard', title: 'Dashboard', url: '/dashboard', icon: <DashboardIcon /> },
    { id: 'shortcuts', title: 'Shortcuts', url: '/shortcuts', icon: <LinkIcon />, requiredPermission: 'shortcuts:read' },
    { id: 'drive', title: 'Drive Files', url: '/drive', icon: <FolderIcon />, requiredPermission: 'googleDrive:read' },
    { id: 'google-calendar', title: 'Google Calendar', url: '/google-calendar', icon: <CalendarIcon />, requiredPermission: 'googleCalendar:read' },
    { id: 'google-forms', title: 'Google Forms', url: '/google-forms', icon: <FormsIcon />, requiredPermission: 'googleForms:read' },
    { id: 'staff-directory', title: 'Staff Directory', url: '/staff-directory', icon: <StaffDirectoryIcon />, requiredPermission: 'staffDirectory:read' },
    { id: 'people', title: 'People Directory', url: '/people', icon: <PeopleIcon />, requiredPermission: 'people:read' },
    { id: 'team-hubs', title: 'Team Hubs', url: '/team-hubs', icon: <PeopleIcon /> },
    { id: 'tasks', title: 'Tasks', url: '/tasks', icon: <TasksIcon />, requiredPermission: 'tasks:read' },
    { id: 'tickets', title: 'Tickets', url: '/tickets', icon: <TicketsIcon />, requiredPermission: 'tickets:read' },
    { id: 'room-booking', title: 'Room Booking', url: '/room-booking', icon: <RoomBookingIcon />, requiredPermission: 'roomBooking:read' },
    { id: 'building-management', title: 'Building Management', url: '/building-management', icon: <BuildingManagementIcon />, requiredPermission: 'buildingManagement:read' },
    { id: 'news', title: 'News', url: '/news', icon: <NewsIcon />, requiredPermission: 'news:read' },
    { id: 'notes', title: 'Notes', url: '/notes', icon: <NotesIcon />, requiredPermission: 'notes:read' },
    { id: 'asset-management', title: 'Asset Management', url: '/asset-management', icon: <AssetManagementIcon />, requiredPermission: 'assets:read' },
    { id: 'forms', title: 'Forms', url: '/forms', icon: <FormsIcon />, requiredPermission: 'forms:read' },
    { id: 'canva', title: 'Canva', url: '/canva', icon: <CanvaIcon />, requiredPermission: 'canva:read' },
    { id: 'glpi', title: 'GLPI', url: '/glpi', icon: <GLPIIcon />, requiredPermission: 'glpi:read' },
    { id: 'planning-center', title: 'Planning Center', url: '/planning-center', icon: <PlanningCenterIcon />, requiredPermission: 'planningCenter:read' },
    { id: 'synology', title: 'Synology', url: '/synology', icon: <SynologyIcon />, requiredPermission: 'synology:read' },
    { id: 'dreo', title: 'Dreo', url: '/dreo', icon: <DreoIcon />, requiredPermission: 'dreo:read' },
    { id: 'lg-thinq', title: 'LG ThinQ', url: '/lg-thinq', icon: <LGThinqIcon />, requiredPermission: 'lgThinq:read' },
    { id: 'rain-bird', title: 'Rain Bird', url: '/rain-bird', icon: <RainBirdIcon />, requiredPermission: 'rainBird:read' },
    { id: 'lenel-s2-netbox', title: 'Lenel S2 NetBox', url: '/lenel-s2-netbox', icon: <LenelS2NetBoxIcon />, requiredPermission: 'lenelS2NetBox:read' },
    { id: 'mosyle-business', title: 'Mosyle Business', url: '/mosyle-business', icon: <MosyleBusinessIcon />, requiredPermission: 'mosyleBusiness:read' },
    { id: 'unifi-access', title: 'UniFi Access', url: '/unifi-access', icon: <UnifiAccessIcon />, requiredPermission: 'unifiAccess:read' },
    { id: 'google-admin', title: 'Google Admin', url: '/google-admin', icon: <GoogleAdminIcon />, requiredPermission: 'googleAdmin:read' },
    { id: 'unifi-network', title: 'UniFi Network', url: '/unifi-network', icon: <UnifiNetworkIcon />, requiredPermission: 'unifiNetwork:read' },
    { id: 'unifi-protect', title: 'UniFi Protect', url: '/unifi-protect', icon: <UnifiProtectIcon />, requiredPermission: 'unifiProtect:read' },
    { id: 'apple-business-manager', title: 'Apple Business Manager', url: '/apple-business-manager', icon: <AppleBusinessManagerIcon />, requiredPermission: 'appleBusinessManager:read' },
    { id: 'phone-book', title: 'Phone Book', url: '/phone-book', icon: <PhoneBookIcon />, requiredPermission: 'contacts:read' },
    { id: 'radius', title: 'RADIUS Server', url: '/radius', icon: <RadiusIcon />, requiredPermission: 'radius:read' },
    { id: 'wiim', title: 'WiiM', url: '/wiim', icon: <WiimIcon />, requiredPermission: 'wiim:read' },
    { id: 'skyportcloud', title: 'SkyportCloud', url: '/skyportcloud', icon: <SkyportCloudIcon />, requiredPermission: 'skyportcloud:read' },
    { id: 'qsys', title: 'Q-SYS', url: '/qsys', icon: <QsysIcon />, requiredPermission: 'qsys:read' },
    { id: 'colorlit', title: 'Colorlit', url: '/colorlit', icon: <ColoritIcon />, requiredPermission: 'colorlit:read' },
    { id: 'zeevee', title: 'ZeeVee', url: '/zeevee', icon: <ZeeVeeIcon />, requiredPermission: 'zeevee:read' },
    { id: 'panasonic', title: 'Panasonic', url: '/panasonic', icon: <PanasonicIcon />, requiredPermission: 'panasonic:read' },
    { id: 'access-control', title: 'Access Control', url: '/access-control', icon: <AccessControlIcon />, requiredPermission: 'accessControl:read' },
    { id: 'help', title: 'Help Center', url: '/help', icon: <HelpIcon />, requiredPermission: 'help:read' },
    { id: 'faq', title: 'FAQs', url: '/help/faq', icon: <FAQIcon />, requiredPermission: 'help:read' },
    // Admin pages
    { id: 'admin-users', title: 'Admin: Users', url: '/admin/users', icon: <PeopleIcon />, requiredPermission: 'users:admin' },
    { id: 'admin-roles', title: 'Admin: Roles', url: '/admin/roles', icon: <VpnKeyIcon />, requiredPermission: 'roles:admin' },
    { id: 'admin-shortcuts', title: 'Admin: Shortcuts', url: '/admin/shortcuts', icon: <LinkIcon />, requiredPermission: 'shortcuts:admin' },
    { id: 'admin-menu', title: 'Admin: Menu', url: '/admin/menu', icon: <MenuIcon />, requiredPermission: 'menu:admin' },
    { id: 'admin-building-management', title: 'Admin: Building', url: '/admin/building-management', icon: <BuildingManagementIcon />, requiredPermission: 'buildingManagement:admin' },
    { id: 'admin-provisioning', title: 'Admin: Provisioning', url: '/admin/provisioning', icon: <SettingsIcon />, requiredPermission: 'provisioning:admin' },
    { id: 'admin-ticket-categories', title: 'Admin: Tickets', url: '/admin/ticket-categories-and-tags', icon: <TicketsIcon />, requiredPermission: 'tickets:admin' }
  ];
  
  // State for the settings dialog
  const [settingsOpen, setSettingsOpen] = useState(false);
  
  // Fetch shortcuts and preferences from the API
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch shortcuts
        const shortcutsRes = await axios.get('/api/shortcuts');
        setShortcuts(shortcutsRes.data);
        
        // Filter portal pages based on user permissions
        const filteredPages = portalPagesList.filter(page => {
          const perm = page.requiredPermission;
          // If no required permission, everyone can access
          if (!perm) return true;
          // Admin can access everything
          if (isAdmin()) return true;
          // Check permission via auth context
          return hasPermission(perm);
        });
        
        // Initialize portal pages with filtered list, plus dynamic Team Hubs for user's teams
        let pages = filteredPages;
        try {
          const meRes = await axios.get('/api/users/me');
          const teamIds = Array.isArray(meRes.data.teams) ? meRes.data.teams : [];
          const teamPages = [];
          for (const tId of teamIds) {
            try {
              const tRes = await axios.get(`/api/staff-directory/teams/${tId}`);
              const t = tRes.data || { _id: tId, name: String(tId) };
              teamPages.push({
                id: `team-hub-${t._id || tId}`,
                title: `Team Hub: ${t.name || tId}`,
                url: `/teams/${t._id || tId}/hub`,
                icon: <PeopleIcon />,
                requiredRoles: []
              });
            } catch (e) {
              // Ignore errors fetching individual team
            }
          }
          pages = [...filteredPages, ...teamPages];
        } catch (e) {
          pages = filteredPages;
        }
        setPortalPages(pages);
        
        // Try to fetch user preferences from the database
        try {
          const preferencesRes = await axios.get('/api/users/me/widget-preferences');
          const floatingShortcutPrefs = preferencesRes.data.floatingShortcut;
          
          if (floatingShortcutPrefs) {
            // If preferences exist in the database, use them and update localStorage
            setFavoriteShortcuts(floatingShortcutPrefs.favoriteShortcuts || []);
            // Ensure we're passing an array to setFavoritePortalPages
            const portalPages = Array.isArray(floatingShortcutPrefs.favoritePortalPages) 
              ? floatingShortcutPrefs.favoritePortalPages 
              : (floatingShortcutPrefs.favoritePortalPages?.default || []);
            setFavoritePortalPages(portalPages);
            setWidgetEnabled(floatingShortcutPrefs.enabled !== false); // Default to true if not specified
            setAlwaysShowLabels(floatingShortcutPrefs.alwaysShowLabels || false); // Get label visibility preference
            
            // Update localStorage with the database values
            localStorage.setItem('favoriteShortcuts', JSON.stringify(floatingShortcutPrefs.favoriteShortcuts || []));
            localStorage.setItem('favoritePortalPages', JSON.stringify(portalPages));
            localStorage.setItem('floatingShortcutWidgetEnabled', floatingShortcutPrefs.enabled !== false ? 'true' : 'false');
            localStorage.setItem('floatingShortcutAlwaysShowLabels', floatingShortcutPrefs.alwaysShowLabels ? 'true' : 'false');
          } else {
            // If no preferences in database, check localStorage
            loadFromLocalStorage(shortcutsRes.data, filteredPages);
          }
        } catch (err) {
          console.error('Error fetching widget preferences:', err);
          // If API call fails, fall back to localStorage
          loadFromLocalStorage(shortcutsRes.data, filteredPages);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching shortcuts:', err);
        setError('Failed to load shortcuts');
        setLoading(false);
      }
    };
    
    // Helper function to load preferences from localStorage
    const loadFromLocalStorage = (shortcutsData, filteredPages) => {
      // Load favorite shortcuts from localStorage
      const storedFavorites = localStorage.getItem('favoriteShortcuts');
      const storedFavoritePortalPages = localStorage.getItem('favoritePortalPages');
      const storedWidgetEnabled = localStorage.getItem('floatingShortcutWidgetEnabled');
      const storedAlwaysShowLabels = localStorage.getItem('floatingShortcutAlwaysShowLabels');
      
      let parsedFavorites = [];
      if (storedFavorites) {
        parsedFavorites = JSON.parse(storedFavorites);
        setFavoriteShortcuts(parsedFavorites);
      } else {
        // Default to top 5 most clicked shortcuts if no favorites are saved
        const sortedShortcuts = [...shortcutsData].sort((a, b) => b.clickCount - a.clickCount);
        parsedFavorites = sortedShortcuts.slice(0, 5).map(s => s._id);
        setFavoriteShortcuts(parsedFavorites);
        localStorage.setItem('favoriteShortcuts', JSON.stringify(parsedFavorites));
      }
      
      let parsedFavoritePortalPages = [];
      if (storedFavoritePortalPages) {
        parsedFavoritePortalPages = JSON.parse(storedFavoritePortalPages);
        // Filter out any pages that the user no longer has access to
        parsedFavoritePortalPages = parsedFavoritePortalPages.filter(pageId => 
          filteredPages.some(page => page.id === pageId)
        );
        setFavoritePortalPages(parsedFavoritePortalPages);
      } else {
        // Default to first 3 portal pages if no favorites are saved
        parsedFavoritePortalPages = filteredPages.slice(0, 3).map(p => p.id);
        setFavoritePortalPages(parsedFavoritePortalPages);
        localStorage.setItem('favoritePortalPages', JSON.stringify(parsedFavoritePortalPages));
      }
      
      // Set widget enabled state
      const isEnabled = storedWidgetEnabled !== 'false';
      setWidgetEnabled(isEnabled);
      
      // Set always show labels state
      const showLabels = storedAlwaysShowLabels === 'true';
      setAlwaysShowLabels(showLabels);
      
      // Save to database in background
      savePreferencesToDatabase(parsedFavorites, parsedFavoritePortalPages, isEnabled, showLabels);
    };
    
    fetchData();
  }, [isAdmin, hasPermission]);
  
  // Function to save preferences to the database
  const savePreferencesToDatabase = async (favorites, favoritePortals, enabled, showLabels) => {
    try {
      setSavingToDb(true);
      setSaveError(null);
      
      await axios.put('/api/users/me/widget-preferences', {
        widgetType: 'floatingShortcut',
        preferences: {
          favoriteShortcuts: favorites,
          favoritePortalPages: favoritePortals,
          enabled: enabled,
          alwaysShowLabels: showLabels
        }
      });
      
      setSavingToDb(false);
    } catch (err) {
      console.error('Error saving widget preferences to database:', err);
      setSaveError('Failed to sync preferences with server');
      setSavingToDb(false);
    }
  };
  
  // Handle toggling the expanded state of the widget
  const handleToggleExpand = () => {
    setExpanded(!expanded);
    
    // If collapsing and in edit mode, exit edit mode
    if (expanded && editMode) {
      setEditMode(false);
      setSearchQuery('');
      setTabValue(0); // Reset to shortcuts tab
    }
  };
  
  // Handle closing the edit mode
  const handleCloseEdit = () => {
    setEditMode(false);
    setSearchQuery('');
    setTabValue(0); // Reset to shortcuts tab
  };
  
  // Handle shortcut click
  const handleShortcutClick = async (id) => {
    try {
      await axios.post(`/api/shortcuts/${id}/click`);
    } catch (err) {
      console.error('Error tracking shortcut click:', err);
    }
    
    // No need to close the toolbar when clicking a shortcut
  };
  
  // Handle toggling edit mode
  const handleToggleEditMode = () => {
    setEditMode(!editMode);
    setSearchQuery('');
  };
  
  // Handle toggling a favorite shortcut
  const handleToggleFavorite = (id) => {
    let newFavorites;
    
    if (favoriteShortcuts.includes(id)) {
      // Remove from favorites
      newFavorites = favoriteShortcuts.filter(shortcutId => shortcutId !== id);
    } else {
      // Add to favorites
      newFavorites = [...favoriteShortcuts, id];
    }
    
    setFavoriteShortcuts(newFavorites);
    localStorage.setItem('favoriteShortcuts', JSON.stringify(newFavorites));
    
    // Save to database
    savePreferencesToDatabase(newFavorites, favoritePortalPages, widgetEnabled, alwaysShowLabels);
  };
  
  // Handle toggling a favorite portal page
  const handleToggleFavoritePortalPage = (id) => {
    let newFavorites;
    
    if (favoritePortalPages.includes(id)) {
      // Remove from favorites
      newFavorites = favoritePortalPages.filter(pageId => pageId !== id);
    } else {
      // Add to favorites
      newFavorites = [...favoritePortalPages, id];
    }
    
    setFavoritePortalPages(newFavorites);
    localStorage.setItem('favoritePortalPages', JSON.stringify(newFavorites));
    
    // Save to database
    savePreferencesToDatabase(favoriteShortcuts, newFavorites, widgetEnabled, alwaysShowLabels);
  };
  
  // Handle opening settings dialog
  const handleOpenSettings = () => {
    setSettingsOpen(true);
  };
  
  // Handle closing settings dialog
  const handleCloseSettings = () => {
    setSettingsOpen(false);
  };
  
  // Handle toggling widget enabled state
  const handleToggleWidgetEnabled = (enabled) => {
    setWidgetEnabled(enabled);
    localStorage.setItem('floatingShortcutWidgetEnabled', enabled ? 'true' : 'false');
    
    // Save to database
    savePreferencesToDatabase(favoriteShortcuts, favoritePortalPages, enabled, alwaysShowLabels);
  };
  
  // Handle toggling always show labels state
  const handleToggleAlwaysShowLabels = (showLabels) => {
    setAlwaysShowLabels(showLabels);
    localStorage.setItem('floatingShortcutAlwaysShowLabels', showLabels ? 'true' : 'false');
    
    // Save to database
    savePreferencesToDatabase(favoriteShortcuts, favoritePortalPages, widgetEnabled, showLabels);
  };
  
  // Filter shortcuts based on search query
  const filteredShortcuts = searchQuery 
    ? shortcuts.filter(shortcut => 
        shortcut.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (shortcut.description && shortcut.description.toLowerCase().includes(searchQuery.toLowerCase())))
    : shortcuts;
  
  // Get favorite shortcuts
  const getFavoriteShortcutsData = () => {
    return shortcuts.filter(shortcut => favoriteShortcuts.includes(shortcut._id));
  };
  
  // Get favorite portal pages
  const getFavoritePortalPagesData = () => {
    return portalPages.filter(page => favoritePortalPages.includes(page.id));
  };
  
  // Handle portal page click
  const handlePortalPageClick = (id) => {
    // No need to track clicks for portal pages
    // No need to close the toolbar when clicking a portal page
  };
  
  // Handle tab change
  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };
  
  // Filter portal pages based on search query
  const filteredPortalPages = searchQuery 
    ? portalPages.filter(page => 
        page.title.toLowerCase().includes(searchQuery.toLowerCase()))
    : portalPages;
  
  // Render the edit panel content
  const renderEditPanel = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      );
    }
    
    if (error) {
      return <Alert severity="error">{error}</Alert>;
    }
    
    return (
      <>
        <Box sx={{ p: 2 }}>
          <TextField
            fullWidth
            placeholder="Search..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            size="small"
            sx={{ mb: 2 }}
          />
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            variant="fullWidth"
            sx={{ mb: 2 }}
          >
            <Tab label="Shortcuts" />
            <Tab label="Portal Pages" />
          </Tabs>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Select your favorites to appear in your toolbar.
          </Typography>
        </Box>
        <Divider />
        
        {tabValue === 0 ? (
          // Shortcuts tab
          <List sx={{ maxHeight: 300, overflow: 'auto' }}>
            {filteredShortcuts.length > 0 ? filteredShortcuts.map((shortcut) => (
              <ListItem 
                key={shortcut._id}
                disablePadding
                secondaryAction={
                  <Checkbox
                    edge="end"
                    icon={<StarBorderIcon />}
                    checkedIcon={<StarIcon />}
                    checked={favoriteShortcuts.includes(shortcut._id)}
                    onChange={() => handleToggleFavorite(shortcut._id)}
                  />
                }
              >
                <ListItemButton onClick={() => handleToggleFavorite(shortcut._id)}>
                  <ListItemIcon>
                    <LinkIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary={shortcut.title} 
                    secondary={shortcut.description}
                  />
                </ListItemButton>
              </ListItem>
            )) : null}
          </List>
        ) : (
          // Portal Pages tab
          <List sx={{ maxHeight: 300, overflow: 'auto' }}>
            {filteredPortalPages.length > 0 ? filteredPortalPages.map((page) => (
              <ListItem 
                key={page.id}
                disablePadding
                secondaryAction={
                  <Checkbox
                    edge="end"
                    icon={<StarBorderIcon />}
                    checkedIcon={<StarIcon />}
                    checked={favoritePortalPages.includes(page.id)}
                    onChange={() => handleToggleFavoritePortalPage(page.id)}
                  />
                }
              >
                <ListItemButton onClick={() => handleToggleFavoritePortalPage(page.id)}>
                  <ListItemIcon>
                    {page.icon}
                  </ListItemIcon>
                  <ListItemText 
                    primary={page.title}
                  />
                </ListItemButton>
              </ListItem>
            )) : null}
          </List>
        )}
      </>
    );
  };
  
  // Render the toolbar content
  const renderToolbarContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 1 }}>
          <CircularProgress size={24} />
        </Box>
      );
    }
    
    if (error) {
      return <Alert severity="error" sx={{ py: 0 }}>{error}</Alert>;
    }
    
    const favoriteShortcutsData = getFavoriteShortcutsData();
    const favoritePortalPagesData = getFavoritePortalPagesData();
    
    if (favoriteShortcutsData.length === 0 && favoritePortalPagesData.length === 0) {
      return (
        <Box sx={{ display: 'flex', alignItems: 'center', px: 2 }}>
          <Typography variant="body2" sx={{ flexGrow: 1 }}>
            No shortcuts selected
          </Typography>
          <Button 
            size="small"
            startIcon={<EditIcon />}
            onClick={handleToggleEditMode}
          >
            Customize
          </Button>
        </Box>
      );
    }
    
    return (
      <Box sx={{ display: 'flex', overflowX: 'auto', px: 1 }}>
        {/* Portal Pages */}
        {favoritePortalPagesData.length > 0 ? favoritePortalPagesData.map((page) => (
          <Box
            key={page.id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
              mx: 0.5,
              transition: 'all 0.7s ease',
              '&:hover': {
                '& .icon-title': {
                  width: alwaysShowLabels ? 'auto' : 'auto',
                  opacity: alwaysShowLabels ? 1 : 1,
                  marginLeft: alwaysShowLabels ? 1 : 1,
                  visibility: alwaysShowLabels ? 'visible' : 'visible',
                },
                zIndex: 10
              }
            }}
          >
            <IconButton
              component={RouterLink}
              to={page.url}
              onClick={() => handlePortalPageClick(page.id)}
              size="medium"
              sx={{ color: inHeader ? 'white' : 'inherit' }}
            >
              {page.icon}
            </IconButton>
            <Typography
              component={RouterLink}
              to={page.url}
              onClick={() => handlePortalPageClick(page.id)}
              className="icon-title"
              variant="body2"
              sx={{
                width: alwaysShowLabels ? 'auto' : 0,
                opacity: alwaysShowLabels ? 1 : 0,
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                transition: 'all 0.7s ease',
                marginLeft: alwaysShowLabels ? 1 : 0,
                visibility: alwaysShowLabels ? 'visible' : 'hidden',
                color: inHeader ? 'white' : 'inherit',
                textDecoration: 'none',
                cursor: 'pointer'
              }}
            >
              {page.title}
            </Typography>
          </Box>
        )) : null}
        
        {/* Shortcuts */}
        {favoriteShortcutsData.length > 0 ? favoriteShortcutsData.map((shortcut) => (
          <Box
            key={shortcut._id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              position: 'relative',
              mx: 0.5,
              transition: 'all 0.7s ease',
              '&:hover': {
                '& .icon-title': {
                  width: alwaysShowLabels ? 'auto' : 'auto',
                  opacity: alwaysShowLabels ? 1 : 1,
                  marginLeft: alwaysShowLabels ? 1 : 1,
                  visibility: alwaysShowLabels ? 'visible' : 'visible',
                },
                zIndex: 10
              }
            }}
          >
            <IconButton
              component="a"
              href={shortcut.url}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleShortcutClick(shortcut._id)}
              size="medium"
              sx={{ color: inHeader ? 'white' : 'inherit' }}
            >
              <LinkIcon sx={{ color: inHeader ? 'white' : 'inherit' }} />
            </IconButton>
            <Typography
              component="a"
              href={shortcut.url}
              target="_blank"
              rel="noopener noreferrer"
              onClick={() => handleShortcutClick(shortcut._id)}
              className="icon-title"
              variant="body2"
              sx={{
                width: alwaysShowLabels ? 'auto' : 0,
                opacity: alwaysShowLabels ? 1 : 0,
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                transition: 'all 0.7s ease',
                marginLeft: alwaysShowLabels ? 1 : 0,
                visibility: alwaysShowLabels ? 'visible' : 'hidden',
                color: inHeader ? 'white' : 'inherit',
                textDecoration: 'none',
                cursor: 'pointer'
              }}
            >
              {shortcut.title}
            </Typography>
          </Box>
        )) : null}
      </Box>
    );
  };
  
  // If in header mode, render inline shortcuts
  if (inHeader) {
    return widgetEnabled ? (
      <>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
          {renderToolbarContent()}
          <Tooltip title={editMode ? "Save" : "Customize"} placement="bottom">
            <IconButton 
              onClick={handleToggleEditMode} 
              size="small" 
              sx={{ color: 'white', ml: 1, display: { xs: 'none', sm: 'inline-flex' } }}
            >
              {editMode ? <CloseIcon /> : <EditIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Settings" placement="bottom">
            <IconButton 
              onClick={handleOpenSettings} 
              size="small" 
              sx={{ color: 'white', display: { xs: 'none', sm: 'inline-flex' } }}
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Edit panel drawer - rendered in header mode with proper edit functionality */}
        <Drawer
          anchor="bottom"
          open={editMode}
          onClose={handleCloseEdit}
          PaperProps={{
            sx: { maxHeight: '70vh' }
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, pb: 1 }}>
            <Typography variant="h6">Customize Quick Links</Typography>
            <IconButton onClick={handleCloseEdit} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
          <Divider />
          {renderEditPanel()}
        </Drawer>

        {/* Settings dialog - rendered in header mode */}
        <Dialog
          open={settingsOpen}
          onClose={handleCloseSettings}
          maxWidth="xs"
          fullWidth
        >
          <DialogTitle>
            Quick Links Settings
            <IconButton
              aria-label="close"
              onClick={handleCloseSettings}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <FormControlLabel
              control={
                <Checkbox
                  checked={widgetEnabled}
                  onChange={(e) => handleToggleWidgetEnabled(e.target.checked)}
                />
              }
              label="Show quick links toolbar"
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              The quick links toolbar provides easy access to your favorite links from anywhere in the portal.
            </Typography>
            
            <Divider sx={{ my: 2 }} />
            
            <FormControlLabel
              control={
                <Switch
                  checked={alwaysShowLabels}
                  onChange={(e) => handleToggleAlwaysShowLabels(e.target.checked)}
                  color="primary"
                />
              }
              label="Always show link names"
            />
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              When enabled, link names will always be visible. When disabled, link names will only appear on hover.
            </Typography>
            
            {savingToDb && (
              <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
                <CircularProgress size={16} sx={{ mr: 1 }} />
                <Typography variant="body2" color="text.secondary">
                  Saving preferences...
                </Typography>
              </Box>
            )}
            
            {saveError && (
              <Alert severity="warning" sx={{ mt: 2 }}>
                {saveError}
              </Alert>
            )}
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Your preferences are saved to your account and will be available on any device you log in from.
              </Typography>
            </Box>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCloseSettings}>Close</Button>
          </DialogActions>
        </Dialog>
      </>
    ) : null;
  }

  return (
    <>
      {/* Floating Widget - Vertically Centered (legacy mode - now disabled when inHeader is true) */}
      {widgetEnabled && !inHeader && (
        <Paper
          elevation={3}
          sx={{
            position: 'fixed',
            right: 100,
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 1000,
            backgroundColor: 'background.paper',
            borderRadius: 2,
            overflow: 'hidden',
            width: expanded ? 'auto' : 48,
            maxWidth: expanded ? '400px' : 48,
            transition: 'width 0.3s ease, max-width 0.3s ease'
          }}
        >
          <Box sx={{ 
            display: 'flex', 
            flexDirection: expanded ? 'row' : 'column',
            alignItems: 'center',
            p: expanded ? 1 : 0.5
          }}>
            {expanded && (
              <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1, px: 1 }}>
                {renderToolbarContent()}
              </Box>
            )}
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              {!expanded && (
                <Tooltip title="Shortcuts" placement="left">
                  <IconButton onClick={handleToggleExpand} size="small">
                    <LinkIcon />
                  </IconButton>
                </Tooltip>
              )}
              <Tooltip title={editMode ? "Save" : "Customize"} placement="left">
                <IconButton onClick={handleToggleEditMode} size="small">
                  {editMode ? <CloseIcon /> : <EditIcon />}
                </IconButton>
              </Tooltip>
              <Tooltip title="Settings" placement="left">
                <IconButton onClick={handleOpenSettings} size="small">
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title={expanded ? "Collapse" : "Expand"} placement="left">
                <IconButton onClick={handleToggleExpand} size="small">
                  {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Paper>
      )}
      
      {/* Edit panel drawer - always rendered so it works in both header and floating modes */}
      <Drawer
        anchor="bottom"
        open={editMode}
        onClose={handleCloseEdit}
        PaperProps={{
          sx: { maxHeight: '70vh', ...(user && isDesktop ? { left: `${drawerWidth}px`, right: 0 } : {}) }
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, pb: 1 }}>
          <Typography variant="h6">Customize Quick Links</Typography>
          <IconButton onClick={handleCloseEdit} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
        <Divider />
        {renderEditPanel()}
      </Drawer>
      
      {/* Expanded panel - only shown in floating mode */}
      {!inHeader && (
        <Drawer
          anchor="bottom"
          open={expanded && !editMode}
          onClose={handleToggleExpand}
          PaperProps={{
            sx: { maxHeight: '50vh', ...(user && isDesktop ? { left: `${drawerWidth}px`, right: 0 } : {}) }
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2, pb: 1 }}>
            <Typography variant="h6">Quick Links</Typography>
            <IconButton onClick={handleToggleExpand} size="small">
              <ExpandLessIcon />
            </IconButton>
          </Box>
          <Divider />
          <List>
            {/* Portal Pages */}
            {getFavoritePortalPagesData().length > 0 ? getFavoritePortalPagesData().map((page) => (
              <ListItem 
                key={page.id}
                component={RouterLink}
                to={page.url}
                onClick={() => handlePortalPageClick(page.id)}
                button
                divider
              >
                <ListItemIcon>
                  {page.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={page.title}
                />
              </ListItem>
            )) : null}
            
            {/* Shortcuts */}
            {getFavoriteShortcutsData().length > 0 ? getFavoriteShortcutsData().map((shortcut) => (
              <ListItem 
                key={shortcut._id}
                component="a"
                href={shortcut.url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={() => handleShortcutClick(shortcut._id)}
                button
                divider
              >
                <ListItemIcon>
                  <LinkIcon />
                </ListItemIcon>
                <ListItemText 
                  primary={shortcut.title} 
                  secondary={shortcut.description}
                />
              </ListItem>
            )) : null}
          </List>
        </Drawer>
      )}
      
      {/* Settings dialog - always rendered so it works in both header and floating modes */}
      <Dialog
        open={settingsOpen}
        onClose={handleCloseSettings}
        maxWidth="xs"
        fullWidth
      >
        <DialogTitle>
          Quick Links Settings
          <IconButton
            aria-label="close"
            onClick={handleCloseSettings}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <FormControlLabel
            control={
              <Checkbox
                checked={widgetEnabled}
                onChange={(e) => handleToggleWidgetEnabled(e.target.checked)}
              />
            }
            label="Show quick links toolbar"
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            The quick links toolbar provides easy access to your favorite links from anywhere in the portal.
          </Typography>
          
          <Divider sx={{ my: 2 }} />
          
          <FormControlLabel
            control={
              <Switch
                checked={alwaysShowLabels}
                onChange={(e) => handleToggleAlwaysShowLabels(e.target.checked)}
                color="primary"
              />
            }
            label="Always show link names"
          />
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            When enabled, link names will always be visible. When disabled, link names will only appear on hover.
          </Typography>
          
          {savingToDb && (
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <CircularProgress size={16} sx={{ mr: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Saving preferences...
              </Typography>
            </Box>
          )}
          
          {saveError && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              {saveError}
            </Alert>
          )}
          
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              Your preferences are saved to your account and will be available on any device you log in from.
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseSettings}>Close</Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default FloatingShortcutWidget;