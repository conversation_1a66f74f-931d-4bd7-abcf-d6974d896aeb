// Client-side static categories service (no network). This replaces DB/API-backed categories.
import { staticMenuCategories as defaultCategories } from '../config/menuConfig';

let memoryCategories = [...defaultCategories];
const clone = (obj) => JSON.parse(JSON.stringify(obj));

/**
 * Get all menu categories
 * @returns {Promise<Array>} Array of category objects
 */
export const getCategories = async () => {
  // return sorted by order then name for consistency
  const cats = [...memoryCategories].sort((a,b) => (a.order||0) - (b.order||0) || String(a.name).localeCompare(String(b.name)));
  return clone(cats);
};

/**
 * Get a menu category by ID
 * @param {string} id Category ID
 * @returns {Promise<Object>} Category object
 */
export const getCategoryById = async (id) => {
  const cat = memoryCategories.find(c => String(c._id) === String(id));
  if (!cat) throw new Error(`Menu category not found: ${id}`);
  return clone(cat);
};

/**
 * Create a new menu category (in-memory)
 * @param {Object} categoryData Category data
 * @returns {Promise<Object>} Created category object
 */
export const createCategory = async (categoryData) => {
  const id = categoryData._id || (categoryData.name ? categoryData.name.toLowerCase().replace(/\s+/g,'-') : `cat-${Date.now()}`);
  const newCat = { _id: id, order: 0, icon: 'folder', color: '#1976d2', ...categoryData };
  memoryCategories.push(newCat);
  return clone(newCat);
};

/**
 * Update a menu category (in-memory)
 * @param {string} id Category ID
 * @param {Object} categoryData Updated category data
 * @returns {Promise<Object>} Updated category object
 */
export const updateCategory = async (id, categoryData) => {
  const idx = memoryCategories.findIndex(c => String(c._id) === String(id));
  if (idx === -1) throw new Error(`Menu category not found: ${id}`);
  memoryCategories[idx] = { ...memoryCategories[idx], ...categoryData, _id: memoryCategories[idx]._id };
  return clone(memoryCategories[idx]);
};

/**
 * Delete a menu category (in-memory)
 * @param {string} id Category ID
 * @returns {Promise<Object>} Response object
 */
export const deleteCategory = async (id) => {
  const before = memoryCategories.length;
  memoryCategories = memoryCategories.filter(c => String(c._id) !== String(id));
  const removed = before !== memoryCategories.length;
  if (!removed) throw new Error(`Menu category not found: ${id}`);
  return { ok: true, deletedId: id };
};

/**
 * Initialize default categories (no-op)
 * @returns {Promise<Array>} Array of category objects
 */
export const initializeDefaultCategories = async () => {
  if (memoryCategories.length === 0) memoryCategories = [...defaultCategories];
  return clone(memoryCategories);
};