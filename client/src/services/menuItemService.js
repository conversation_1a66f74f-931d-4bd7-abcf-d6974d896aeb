// Client-side static menu service (no network). This replaces DB/API-backed menu.
import { staticMenuItems as defaultItems } from '../config/menuConfig';

// In-memory store so admin pages can make changes during a session (non-persistent)
let memoryItems = [...defaultItems];

const clone = (obj) => JSON.parse(JSON.stringify(obj));

/**
 * Get all menu items
 * @param {Object} filters Optional filters (category, type)
 * @returns {Promise<Array>} Array of menu item objects
 */
export const getMenuItems = async (filters = {}) => {
  const { category, type } = filters || {};
  let items = memoryItems;
  if (type) items = items.filter(it => (it.type || 'regular') === type);
  if (category) items = items.filter(it => (it.categories || []).includes(category));
  return clone(items);
};

/**
 * Search menu items (client-side)
 * @param {string} searchTerm Search term
 * @returns {Promise<Array>} Array of menu item objects
 */
export const searchMenuItems = async (searchTerm = '') => {
  const term = searchTerm.toLowerCase();
  const items = memoryItems.filter(it => {
    const title = (it.friendlyName || it.title || it.text || '').toLowerCase();
    const description = (it.description || '').toLowerCase();
    const categories = (it.categories || []).join(' ').toLowerCase();
    return term ? (title.includes(term) || description.includes(term) || categories.includes(term)) : true;
  });
  return clone(items);
};

/**
 * Get all unique categories used by menu items
 * @returns {Promise<Array>} Array of category names
 */
export const getMenuItemCategories = async () => {
  const set = new Set();
  memoryItems.forEach(it => (it.categories || []).forEach(c => set.add(c)));
  return Array.from(set);
};

/**
 * Get a menu item by ID
 * @param {string} id Menu item ID
 * @returns {Promise<Object>} Menu item object
 */
export const getMenuItemById = async (id) => {
  const found = memoryItems.find(it => String(it._id) === String(id));
  if (!found) throw new Error(`Menu item not found: ${id}`);
  return clone(found);
};

/**
 * Create a new menu item (in-memory)
 * @param {Object} menuItemData Menu item data
 * @returns {Promise<Object>} Created menu item object
 */
export const createMenuItem = async (menuItemData) => {
  const id = menuItemData._id || (menuItemData.path ? menuItemData.path.replace(/^\//,'').replace(/\//g,'-') : `custom-${Date.now()}`);
  const newItem = { _id: id, type: 'regular', ...menuItemData };
  memoryItems.push(newItem);
  return clone(newItem);
};

/**
 * Update a menu item (in-memory)
 * @param {string} id Menu item ID
 * @param {Object} menuItemData Updated menu item data
 * @returns {Promise<Object>} Updated menu item object
 */
export const updateMenuItem = async (id, menuItemData) => {
  const idx = memoryItems.findIndex(it => String(it._id) === String(id));
  if (idx === -1) throw new Error(`Menu item not found: ${id}`);
  memoryItems[idx] = { ...memoryItems[idx], ...menuItemData, _id: memoryItems[idx]._id };
  return clone(memoryItems[idx]);
};

/**
 * Delete a menu item (in-memory)
 * @param {string} id Menu item ID
 * @returns {Promise<Object>} Response object
 */
export const deleteMenuItem = async (id) => {
  const before = memoryItems.length;
  memoryItems = memoryItems.filter(it => String(it._id) !== String(id));
  const removed = before !== memoryItems.length;
  if (!removed) throw new Error(`Menu item not found: ${id}`);
  return { ok: true, deletedId: id };
};

/**
 * Initialize default menu items (no-op, ensure defaults present)
 * @returns {Promise<Array>} Array of menu item objects
 */
export const initializeDefaultMenuItems = async () => {
  if (memoryItems.length === 0) memoryItems = [...defaultItems];
  return clone(memoryItems);
};

/**
 * Sync new default menu items (non-destructive)
 * @returns {Promise<Object>} Object with createdCount and created items
 */
export const syncNewMenuItems = async () => {
  const existingIds = new Set(memoryItems.map(it => String(it._id)));
  const toAdd = defaultItems.filter(it => !existingIds.has(String(it._id)));
  memoryItems = [...memoryItems, ...toAdd];
  return { createdCount: toAdd.length, created: clone(toAdd) };
};