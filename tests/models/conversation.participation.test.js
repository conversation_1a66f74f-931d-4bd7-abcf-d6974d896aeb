const mongoose = require('mongoose');
const Conversation = require('../../models/Conversation');

describe('Conversation participants logic', () => {
  const uid1 = new mongoose.Types.ObjectId();
  const uid2 = new mongoose.Types.ObjectId();

  test('isParticipant works with ObjectIds and strings', () => {
    const conv = new Conversation({
      participants: [uid1, uid2],
      createdBy: uid1
    });

    expect(conv.isParticipant(uid1)).toBe(true);
    expect(conv.isParticipant(uid2)).toBe(true);
    expect(conv.isParticipant(uid1.toString())).toBe(true);
    expect(conv.isParticipant(uid2.toString())).toBe(true);

    const uid3 = new mongoose.Types.ObjectId();
    expect(conv.isParticipant(uid3)).toBe(false);
  });

  test('isParticipant works when participants are populated docs', () => {
    const conv = new Conversation({
      participants: [{ _id: uid1 }, { _id: uid2 }],
      createdBy: uid1
    });

    expect(conv.isParticipant(uid1)).toBe(true);
    expect(conv.isParticipant(uid2.toString())).toBe(true);

    const uid3 = new mongoose.Types.ObjectId();
    expect(conv.isParticipant(uid3)).toBe(false);
  });

  test('getOtherParticipant returns the other participant in 1-on-1', () => {
    const conv = new Conversation({
      isGroup: false,
      participants: [uid1, uid2],
      createdBy: uid1
    });

    const otherFor1 = conv.getOtherParticipant(uid1);
    expect(otherFor1.toString()).toBe(uid2.toString());

    const otherFor2 = conv.getOtherParticipant(uid2.toString());
    expect(otherFor2.toString()).toBe(uid1.toString());
  });

  test('incrementUnreadForAll handles populated participants and excludes sender', async () => {
    const conv = new Conversation({
      participants: [{ _id: uid1 }, { _id: uid2 }],
      createdBy: uid1
    });

    // Mock save to avoid DB calls
    conv.save = jest.fn().mockResolvedValue(conv);

    await conv.incrementUnreadForAll(uid1);

    expect(conv.unreadCounts.get(uid2.toString())).toBe(1);
    expect(conv.unreadCounts.get(uid1.toString())).toBe(undefined);

    await conv.incrementUnreadForAll(uid2.toString());

    expect(conv.unreadCounts.get(uid1.toString())).toBe(1);
    expect(conv.unreadCounts.get(uid2.toString())).toBe(1); // unchanged for sender
  });

  test('markAsRead sets current user unread to 0 and saves', async () => {
    const conv = new Conversation({
      participants: [uid1, uid2],
      createdBy: uid1
    });
    conv.unreadCounts.set(uid1.toString(), 5);
    conv.save = jest.fn().mockResolvedValue(conv);

    await conv.markAsRead(uid1);
    expect(conv.unreadCounts.get(uid1.toString())).toBe(0);
    expect(conv.save).toHaveBeenCalled();
  });
});
