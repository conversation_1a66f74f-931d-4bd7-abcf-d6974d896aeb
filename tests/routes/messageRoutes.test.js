const path = require('path');

// Mock auth and permission middleware to simple pass-throughs to avoid side effects
const authPath = path.resolve(__dirname, '../..', 'middleware', 'auth');
const checkPermissionPath = path.resolve(__dirname, '../..', 'middleware', 'checkPermission');

jest.mock(authPath, () => ({
  isAuthenticated: (req, res, next) => next(),
  hasRoles: () => (req, res, next) => next(),
  hasPermission: () => (req, res, next) => next()
}));

jest.mock(checkPermissionPath, () => () => (req, res, next) => next());

describe('server/routes/messageRoutes', () => {
  beforeEach(() => {
    jest.resetModules();
  });

  it('requires without throwing and exports an Express router', () => {
    const routerPath = path.resolve(__dirname, '../..', 'server', 'routes', 'messageRoutes');
    expect(() => require(routerPath)).not.toThrow();
    const router = require(routerPath);
    expect(router).toBeTruthy();
    expect(typeof router).toBe('function'); // Express router is a function (middleware)
    expect(typeof router.use).toBe('function');
    expect(typeof router.get).toBe('function');
    expect(typeof router.patch).toBe('function');
  });
});
