const request = require('supertest');
const express = require('express');

// Mock auth to inject a user id for /me
jest.mock('../../../middleware/auth', () => ({
  isAuthenticated: (req, res, next) => {
    req.user = { id: '5f7d7e1c9d3e2a1b3c4d5e6f' };
    next();
  },
  hasRoles: () => (req, res, next) => next()
}));

// Mock websocket server to avoid side effects
jest.mock('../../../server/websocket/websocketServer', () => ({
  broadcast: jest.fn(),
  getOnlineUsers: jest.fn(() => [])
}));

// Mock User model for /me lookup and save
jest.mock('../../../models/User', () => {
  const mockUserDoc = {
    _id: '5f7d7e1c9d3e2a1b3c4d5e6f',
    presence: {},
    save: jest.fn().mockResolvedValue(true)
  };

  return {
    findById: jest.fn().mockImplementation((id) => {
      if (id === '5f7d7e1c9d3e2a1b3c4d5e6f') {
        return Promise.resolve(mockUserDoc);
      }
      return Promise.resolve(null);
    })
  };
});

const app = express();
app.use(express.json());
app.use('/api/users', require('../../../routes/api/users'));

describe('PUT /api/users/me/status', () => {
  it('updates presence with status and optional statusMessage, without requiring isActive', async () => {
    const res = await request(app)
      .put('/api/users/me/status')
      .send({ status: 'away', statusMessage: 'brb' });

    expect(res.statusCode).toBe(200);
    expect(res.body).toHaveProperty('presence');
    expect(res.body.presence).toHaveProperty('status', 'away');
    expect(res.body.presence).toHaveProperty('statusMessage', 'brb');
  });

  it('returns validation error for invalid status, not for missing isActive', async () => {
    const res = await request(app)
      .put('/api/users/me/status')
      .send({});

    expect(res.statusCode).toBe(400);
    expect(res.body).toHaveProperty('errors');
    const params = res.body.errors.map(e => e.param);
    expect(params).toContain('status');
    expect(params).not.toContain('isActive');
  });
});


describe('PUT /api/users/:id/status (admin route) remains intact', () => {
  it('requires isActive boolean for valid ObjectId ids, and does not capture /me/status', async () => {
    // Should return 400 because missing isActive
    const res1 = await request(app)
      .put('/api/users/5f7d7e1c9d3e2a1b3c4d5e6f/status')
      .send({});
    expect(res1.statusCode).toBe(400);
    const params1 = res1.body.errors.map(e => e.param);
    expect(params1).toContain('isActive');

    // Ensure '/me/status' is not captured by '/:id/status' route
    const res2 = await request(app)
      .put('/api/users/me/status')
      .send({ status: 'available' });
    expect(res2.statusCode).toBe(200);
    expect(res2.body.presence.status).toBe('available');
  });
});
