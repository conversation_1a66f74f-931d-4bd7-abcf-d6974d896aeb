/**
 * Tests for UniFi Access getDoors() API prefix fallback
 */

jest.mock('axios', () => {
  const instance = {
    defaults: { headers: { common: {} }, httpsAgent: {} },
    get: jest.fn()
  };

  // Top-level axios object with create() and get()
  const axios = {
    create: jest.fn(() => instance),
    get: jest.fn(),
    defaults: { headers: { common: {} } }
  };

  // Helper to reset behavior in tests
  axios.__instance = instance;

  return axios;
});

const axios = require('axios');
const UnifiAccessAPI = require('../../../server/integrations/unifiAccess/unifiAccessAPI');

describe('UnifiAccessAPI.getDoors prefix fallback', () => {
  const host = 'example.com';
  const port = '443';
  const apiKey = 'dummy-key';

  beforeEach(() => {
    jest.resetModules();

    process.env.UNIFI_ACCESS_HOST = host;
    process.env.UNIFI_ACCESS_PORT = port;
    process.env.UNIFI_ACCESS_API_KEY = apiKey;
    delete process.env.UNIFI_ACCESS_API_PREFIX; // ensure default is used

    // Clear mocks
    axios.create.mockClear();
    axios.get.mockClear();
    axios.__instance.get.mockClear();

    // Default: instance.get resolves to doors to support post-validation calls
    axios.__instance.get.mockResolvedValue({ data: [{ id: 'door1' }] });

    // Configure top-level axios.get to simulate prefix probing behavior
    axios.get.mockImplementation((url) => {
      if (url.startsWith(`https://${host}:${port}/v1/doors`)) {
        // Simulate 404 for default '/v1'
        return Promise.reject({ response: { status: 404 } });
      }
      if (url.startsWith(`https://${host}:${port}/api/v1/doors`)) {
        // Success on '/api/v1'
        return Promise.resolve({ data: [{ id: 'door1' }] });
      }
      // Any other trial should be 404 to continue probing
      return Promise.reject({ response: { status: 404 } });
    });
  });

  it('falls back from /v1 to /api/v1 and caches working prefix', async () => {
    const api = new UnifiAccessAPI();

    // First call should probe and find /api/v1
    const doorsFirst = await api.getDoors();
    expect(Array.isArray(doorsFirst)).toBe(true);
    expect(doorsFirst[0].id).toBe('door1');

    // After success, apiPrefixValidated should be true and baseURL updated
    expect(api.apiPrefixValidated).toBe(true);
    expect(api.baseURL).toBe(`https://${host}:${port}/api/v1`);

    // Second call should use the instance axios.get with relative path
    const doorsSecond = await api.getDoors();
    expect(Array.isArray(doorsSecond)).toBe(true);
    expect(axios.__instance.get).toHaveBeenCalledWith('/doors');
  });
});
