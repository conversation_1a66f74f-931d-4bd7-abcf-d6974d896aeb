const LenelS2NetBoxAPI = require('../../../server/integrations/lenelS2NetBox/lenelS2NetBoxAPI');

/**
 * Verifies transformElevatorsData uses KEY when ELEVATORKEY is absent
 * and does not log the defaulting warning.
 */
describe('LenelS2NetBoxAPI.transformElevatorsData KEY fallback', () => {
  let api;
  let warnSpy;

  beforeEach(() => {
    api = new LenelS2NetBoxAPI('host', 'user', 'pass');
    warnSpy = jest.spyOn(console, 'warn').mockImplementation(() => {});
  });

  afterEach(() => {
    warnSpy.mockRestore();
  });

  it('uses DETAILS.ELEVATORS[].ELEVATOR[].KEY when ELEVATORKEY is missing', () => {
    const mock = {
      NETBOX: {
        RESPONSE: [
          {
            CODE: ['SUCCESS'],
            DETAILS: [
              {
                ELEVATORS: [
                  {
                    ELEVATOR: [
                      {
                        KEY: ['1'],
                        NAME: ['Elevator 00'],
                        STATUS: ['Online']
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    };

    const result = api.transformElevatorsData(mock);
    expect(result).toHaveLength(1);
    expect(result[0]).toMatchObject({ id: '1', key: '1', name: 'Elevator 00', online: true });

    // Should not warn about missing key anymore
    const calls = warnSpy.mock.calls.map(args => args[0] && String(args[0]));
    expect(calls.find(msg => typeof msg === 'string' && msg.includes('missing') && msg.includes('ELEVATORKEY/KEY'))).toBeUndefined();
  });
});
