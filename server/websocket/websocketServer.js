const WebSocket = require('ws');
const { v4: uuidv4 } = require('uuid');

class WebSocketServer {
  constructor() {
    this.wss = null;
    this.clients = new Map();
    this.subscriptions = new Map(); // eventType -> Set of client IDs
  }

  /**
   * Initialize WebSocket server
   * @param {Object} server - HTTP server instance
   */
  initialize(server) {
    this.wss = new WebSocket.Server({ 
      server,
      path: '/ws',
      perMessageDeflate: {
        zlibDeflateOptions: {
          // See zlib defaults.
          chunkSize: 1024,
          memLevel: 7,
          level: 3
        },
        zlibInflateOptions: {
          chunkSize: 10 * 1024
        },
        // Other options settable:
        clientNoContextTakeover: true, // Defaults to negotiated value.
        serverNoContextTakeover: true, // Defaults to negotiated value.
        serverMaxWindowBits: 10, // Defaults to negotiated value.
        // Below options specified as default values.
        concurrencyLimit: 10, // Limits zlib concurrency for perf.
        threshold: 1024 // Size (in bytes) below which messages should not be compressed.
      },
      maxPayload: 50 * 1024 * 1024, // 50 MB max payload
      clientTracking: true, // Enable client tracking
      backlog: 100 // Maximum length of pending connections queue
    });

    this.wss.on('connection', (ws, req) => {
      const clientId = uuidv4();
      const now = new Date();
      const clientInfo = {
        id: clientId,
        ws: ws,
        subscriptions: new Set(),
        connectedAt: now,
        lastPing: now,
        isAlive: true,
        ip: req.socket.remoteAddress,
        userAgent: req.headers['user-agent'] || 'Unknown'
      };
      
      // Log detailed connection information
      console.log(`WebSocket client connected: ${clientId}`, {
        ip: clientInfo.ip,
        userAgent: clientInfo.userAgent,
        timestamp: now.toISOString()
      });

      this.clients.set(clientId, clientInfo);

      // Send welcome message
      this.sendToClient(clientId, {
        type: 'welcome',
        clientId: clientId,
        timestamp: new Date().toISOString()
      });

      // Handle incoming messages
      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          this.handleClientMessage(clientId, message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          this.sendToClient(clientId, {
            type: 'error',
            message: 'Invalid JSON message'
          });
        }
      });

      // Handle client disconnect
      ws.on('close', () => {
        console.log(`WebSocket client disconnected: ${clientId}`);
        this.removeClient(clientId);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error(`WebSocket error for client ${clientId}:`, error);
        this.removeClient(clientId);
      });

      // Set up ping/pong for connection health
      ws.on('pong', () => {
        if (this.clients.has(clientId)) {
          const client = this.clients.get(clientId);
          client.lastPing = new Date();
          client.isAlive = true;
          console.log(`Received pong from client ${clientId}`);
        }
      });
      
      // Set initial isAlive flag
      if (this.clients.has(clientId)) {
        this.clients.get(clientId).isAlive = true;
      }
      
      // Handle unexpected errors
      ws.on('unexpected-response', (request, response) => {
        console.error(`WebSocket unexpected response for client ${clientId}:`, {
          status: response.statusCode,
          headers: response.headers
        });
      });
    });

    // Define ping interval constants
    this.pingIntervalMs = 30000; // 30 seconds between ping checks
    this.staleThresholdMs = 60000; // 1 minute without response is considered stale
    
    // Set up periodic ping to check connection health
    this.pingInterval = setInterval(() => {
      this.pingClients();
    }, this.pingIntervalMs);
    
    console.log(`WebSocket ping interval set to ${this.pingIntervalMs}ms, stale threshold set to ${this.staleThresholdMs}ms`);

    console.log('WebSocket server initialized');
  }

  /**
   * Handle incoming message from client
   * @param {string} clientId - Client ID
   * @param {Object} message - Parsed message
   */
  handleClientMessage(clientId, message) {
    const { type, eventType, userId } = message;

    switch (type) {
      case 'subscribe':
        this.subscribeClient(clientId, eventType);
        break;
      
      case 'unsubscribe':
        this.unsubscribeClient(clientId, eventType);
        break;
      
      case 'ping':
        this.sendToClient(clientId, { type: 'pong' });
        break;
      
      case 'authenticate':
        this.authenticateClient(clientId, userId);
        break;
      
      default:
        console.warn(`Unknown message type from client ${clientId}:`, type);
        this.sendToClient(clientId, {
          type: 'error',
          message: `Unknown message type: ${type}`
        });
    }
  }

  /**
   * Associate client with user ID
   * @param {string} clientId - Client ID
   * @param {string} userId - User ID to associate
   */
  authenticateClient(clientId, userId) {
    if (!this.clients.has(clientId) || !userId) return;

    const client = this.clients.get(clientId);
    client.userId = userId.toString();
    
    console.log(`Client ${clientId} authenticated as user ${userId}`);
    
    this.sendToClient(clientId, {
      type: 'authenticated',
      userId: userId
    });

    // Update user's online status in database
    this.updateUserOnlineStatus(userId, true);
  }

  /**
   * Subscribe client to event type
   * @param {string} clientId - Client ID
   * @param {string} eventType - Event type to subscribe to
   */
  subscribeClient(clientId, eventType) {
    if (!this.clients.has(clientId)) return;

    // Add to client's subscriptions
    this.clients.get(clientId).subscriptions.add(eventType);

    // Add to global subscriptions map
    if (!this.subscriptions.has(eventType)) {
      this.subscriptions.set(eventType, new Set());
    }
    this.subscriptions.get(eventType).add(clientId);

    console.log(`Client ${clientId} subscribed to ${eventType}`);
    
    this.sendToClient(clientId, {
      type: 'subscribed',
      eventType: eventType
    });
  }

  /**
   * Unsubscribe client from event type
   * @param {string} clientId - Client ID
   * @param {string} eventType - Event type to unsubscribe from
   */
  unsubscribeClient(clientId, eventType) {
    if (!this.clients.has(clientId)) return;

    // Remove from client's subscriptions
    this.clients.get(clientId).subscriptions.delete(eventType);

    // Remove from global subscriptions map
    if (this.subscriptions.has(eventType)) {
      this.subscriptions.get(eventType).delete(clientId);
      
      // Clean up empty subscription sets
      if (this.subscriptions.get(eventType).size === 0) {
        this.subscriptions.delete(eventType);
      }
    }

    console.log(`Client ${clientId} unsubscribed from ${eventType}`);
    
    this.sendToClient(clientId, {
      type: 'unsubscribed',
      eventType: eventType
    });
  }

  /**
   * Remove client and clean up subscriptions
   * @param {string} clientId - Client ID to remove
   */
  removeClient(clientId) {
    if (!this.clients.has(clientId)) return;

    const client = this.clients.get(clientId);
    const userId = client.userId;
    
    // Remove from all subscriptions
    client.subscriptions.forEach(eventType => {
      if (this.subscriptions.has(eventType)) {
        this.subscriptions.get(eventType).delete(clientId);
        
        // Clean up empty subscription sets
        if (this.subscriptions.get(eventType).size === 0) {
          this.subscriptions.delete(eventType);
        }
      }
    });

    // Remove client
    this.clients.delete(clientId);

    // Check if user has other active connections
    if (userId) {
      const hasOtherConnections = Array.from(this.clients.values()).some(c => c.userId === userId);
      if (!hasOtherConnections) {
        // Update user's offline status in database
        this.updateUserOnlineStatus(userId, false);
      }
    }
  }

  /**
   * Send message to specific client
   * @param {string} clientId - Client ID
   * @param {Object} message - Message to send
   */
  sendToClient(clientId, message) {
    if (!this.clients.has(clientId)) return false;

    const client = this.clients.get(clientId);
    
    if (client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error(`Error sending message to client ${clientId}:`, error);
        this.removeClient(clientId);
        return false;
      }
    } else {
      this.removeClient(clientId);
      return false;
    }
  }

  /**
   * Send message to a specific user by userId
   * @param {string} userId - User ID to send message to
   * @param {string} eventType - Event type
   * @param {Object} data - Data to send
   */
  sendToUser(userId, eventType, data) {
    // Find all clients for this user
    for (const [clientId, clientInfo] of this.clients) {
      if (clientInfo.userId === userId.toString()) {
        this.sendToClient(clientId, {
          type: eventType,
          data: data,
          timestamp: new Date().toISOString()
        });
      }
    }
  }

  /**
   * Send message to all participants in a conversation
   * @param {string} conversationId - Conversation ID
   * @param {string} eventType - Event type
   * @param {Object} data - Data to send
   */
  sendToConversation(conversationId, eventType, data) {
    // This would need to look up conversation participants
    // For now, we'll implement basic broadcast to subscribed clients
    this.broadcast(`conversation:${conversationId}`, {
      type: eventType,
      data: data,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Broadcast event to all subscribed clients
   * @param {string} eventType - Event type
   * @param {Object} payload - Event payload
   */
  broadcast(eventType, payload) {
    if (!this.subscriptions.has(eventType)) return;

    const message = {
      type: 'event',
      eventType: eventType,
      payload: payload,
      timestamp: new Date().toISOString()
    };

    const subscribedClients = this.subscriptions.get(eventType);
    let successCount = 0;
    let failureCount = 0;

    subscribedClients.forEach(clientId => {
      if (this.sendToClient(clientId, message)) {
        successCount++;
      } else {
        failureCount++;
      }
    });

    console.log(`Broadcasted ${eventType} to ${successCount} clients (${failureCount} failures)`);
  }

  /**
   * Broadcast debug data to subscribed debug clients
   * @param {string} subType - Debug data sub-type (api_request, external_api_request, etc.)
   * @param {Object} data - Debug data
   */
  broadcastDebugData(subType, data) {
    this.broadcast('debug', {
      subType,
      data
    });
  }

  /**
   * Ping all clients to check connection health
   */
  pingClients() {
    const now = new Date();
    // Use class properties for consistency
    const staleThreshold = this.staleThresholdMs;
    const pingInterval = this.pingIntervalMs;

    this.clients.forEach((client, clientId) => {
      if (client.ws.readyState === WebSocket.OPEN) {
        // Check if client is stale based on last ping response
        if (now - client.lastPing > staleThreshold) {
          console.log(`Removing stale client: ${clientId} (no ping response for ${Math.round((now - client.lastPing)/1000)}s)`);
          this.removeClient(clientId);
          return;
        }
        
        // Check if client is alive based on isAlive flag
        if (client.isAlive === false) {
          console.log(`Terminating client ${clientId} (failed to respond to ping)`);
          client.ws.terminate();
          this.removeClient(clientId);
          return;
        }
        
        // Only send ping if enough time has passed since last ping
        if (now - client.lastPing > pingInterval) {
          // Reset isAlive flag before sending ping
          client.isAlive = false;
          
          try {
            // Send ping with a payload for better debugging
            client.ws.ping(Buffer.from(JSON.stringify({ timestamp: now.toISOString() })));
            console.log(`Sent ping to client ${clientId}`);
          } catch (error) {
            console.error(`Error sending ping to client ${clientId}:`, error);
            this.removeClient(clientId);
          }
        }
      } else {
        console.log(`Removing client ${clientId} (connection not open, state: ${client.ws.readyState})`);
        this.removeClient(clientId);
      }
    });
  }

  /**
   * Update user's online status in database
   * @param {string} userId - User ID
   * @param {boolean} isOnline - Whether user is online
   */
  async updateUserOnlineStatus(userId, isOnline) {
    try {
      const User = require('../../models/User');
      const user = await User.findById(userId);
      if (!user) return;

      if (!user.presence) {
        user.presence = {};
      }

      const now = new Date();
      user.presence.isOnline = isOnline;
      user.presence.lastSeenAt = now;
      
      if (isOnline) {
        user.presence.lastActiveAt = now;
      }

      await user.save();
      
      // Broadcast online status change to all clients
      this.broadcast('userOnlineStatus', {
        userId: userId,
        isOnline: isOnline,
        lastSeenAt: now.toISOString(),
        status: user.presence.status || 'available',
        statusMessage: user.presence.statusMessage || ''
      });

      console.log(`User ${userId} marked as ${isOnline ? 'online' : 'offline'}`);
    } catch (error) {
      console.error(`Error updating online status for user ${userId}:`, error);
    }
  }

  /**
   * Get online users
   * @returns {Array} List of online user IDs
   */
  getOnlineUsers() {
    const onlineUsers = new Set();
    this.clients.forEach(client => {
      if (client.userId) {
        onlineUsers.add(client.userId);
      }
    });
    return Array.from(onlineUsers);
  }

  /**
   * Get server statistics
   * @returns {Object} Server stats
   */
  getStats() {
    const subscriptionStats = {};
    this.subscriptions.forEach((clients, eventType) => {
      subscriptionStats[eventType] = clients.size;
    });

    const onlineUsers = this.getOnlineUsers();

    return {
      connectedClients: this.clients.size,
      onlineUsers: onlineUsers.length,
      onlineUserIds: onlineUsers,
      subscriptions: subscriptionStats,
      totalSubscriptions: Array.from(this.subscriptions.values()).reduce((sum, clients) => sum + clients.size, 0)
    };
  }

  /**
   * Shutdown WebSocket server
   */
  shutdown() {
    if (this.wss) {
      // Clear the ping interval to prevent memory leaks
      if (this.pingInterval) {
        clearInterval(this.pingInterval);
        this.pingInterval = null;
        console.log('WebSocket ping interval cleared');
      }
      
      // Close all client connections gracefully
      this.clients.forEach((client, clientId) => {
        try {
          console.log(`Closing connection to client ${clientId}`);
          client.ws.close(1000, 'Server shutting down');
        } catch (error) {
          console.error(`Error closing connection to client ${clientId}:`, error);
        }
      });
      
      // Close the WebSocket server
      this.wss.close();
      
      // Clear client and subscription maps
      this.clients.clear();
      this.subscriptions.clear();
      
      console.log('WebSocket server shut down');
    }
  }
}

// Create singleton instance
const websocketServer = new WebSocketServer();

module.exports = websocketServer;
