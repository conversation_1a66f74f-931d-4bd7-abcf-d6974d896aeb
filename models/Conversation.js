const mongoose = require('mongoose');

const conversationSchema = new mongoose.Schema({
  participants: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }],
  isGroup: {
    type: Boolean,
    default: false
  },
  groupName: {
    type: String
  },
  groupAvatar: {
    type: String
  },
  lastMessage: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Message'
  },
  lastMessageAt: {
    type: Date,
    default: Date.now
  },
  unreadCounts: {
    type: Map,
    of: Number,
    default: new Map()
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  }
}, {
  timestamps: true
});

// Index for faster queries
conversationSchema.index({ participants: 1, lastMessageAt: -1 });
conversationSchema.index({ 'participants': 1, 'isActive': 1 });

// Virtual for participant count
conversationSchema.virtual('participantCount').get(function() {
  return this.participants.length;
});

// Method to check if user is participant
conversationSchema.methods.isParticipant = function(userId) {
  const uid = userId && userId.toString();
  return this.participants.some(p => {
    const pid = (p && p._id ? p._id : p);
    return pid && pid.toString() === uid;
  });
};

// Method to get other participant in 1-on-1 conversation
conversationSchema.methods.getOtherParticipant = function(userId) {
  if (this.isGroup) return null;
  const uid = userId && userId.toString();
  const other = this.participants.find(p => {
    const pid = (p && p._id ? p._id : p);
    return pid && pid.toString() !== uid;
  });
  return other && other._id ? other._id : other;
};

// Method to mark messages as read for a user
conversationSchema.methods.markAsRead = function(userId) {
  this.unreadCounts.set(userId.toString(), 0);
  return this.save();
};

// Method to increment unread count for all participants except sender
conversationSchema.methods.incrementUnreadForAll = function(senderId) {
  const sid = senderId && senderId.toString();
  this.participants.forEach(p => {
    const pidObj = (p && p._id ? p._id : p);
    const pid = pidObj && pidObj.toString();
    if (pid && pid !== sid) {
      const currentCount = this.unreadCounts.get(pid) || 0;
      this.unreadCounts.set(pid, currentCount + 1);
    }
  });
  return this.save();
};

module.exports = mongoose.model('Conversation', conversationSchema);