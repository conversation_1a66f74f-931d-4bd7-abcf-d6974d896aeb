const mongoose = require('mongoose');
const Schema = mongoose.Schema;

/**
 * Floor Schema
 * Represents a floor within a building in the building management system
 */
const FloorSchema = new Schema({
  buildingId: {
    type: Schema.Types.ObjectId,
    ref: 'Building',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  level: {
    type: Number,
    required: true
  },
  description: {
    type: String,
    trim: true
  },
  floorplan: {
    filename: { type: String },
    originalFilename: { type: String },
    path: { type: String },
    mimetype: { type: String },
    size: { type: Number },
    uploadDate: { type: Date, default: Date.now }
  },
  floorplan3D: {
    ceilingHeight: { type: Number, default: 3 },
    objects: {
      walls: [{
        id: { type: Number },
        start: {
          x: { type: Number },
          z: { type: Number }
        },
        end: {
          x: { type: Number },
          z: { type: Number }
        },
        height: { type: Number },
        thickness: { type: Number },
        color: { type: Number }
      }],
      doors: [{
        id: { type: Number },
        position: {
          x: { type: Number },
          y: { type: Number },
          z: { type: Number }
        },
        rotation: { type: Number },
        width: { type: Number },
        height: { type: Number }
      }],
      windows: [{
        id: { type: Number },
        position: {
          x: { type: Number },
          y: { type: Number },
          z: { type: Number }
        },
        rotation: { type: Number },
        width: { type: Number },
        height: { type: Number }
      }],
      furniture: [{
        id: { type: Number },
        furnitureId: { type: String },
        position: {
          x: { type: Number },
          y: { type: Number },
          z: { type: Number }
        },
        rotation: { type: Number },
        color: { type: Number }
      }]
    },
    metadata: {
      createdAt: { type: Date },
      version: { type: String }
    }
  },
  dimensions: {
    width: { type: Number }, // in feet or meters
    length: { type: Number }, // in feet or meters
    squareFootage: { type: Number }
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'under_construction', 'maintenance'],
    default: 'active'
  },
  metadata: {
    occupancy: { type: Number }, // maximum occupancy
    publicAccess: { type: Boolean, default: true },
    notes: { type: String }
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Create index for faster queries
FloorSchema.index({ buildingId: 1, level: 1 }, { unique: true });
FloorSchema.index({ status: 1 });

module.exports = mongoose.model('Floor', FloorSchema);